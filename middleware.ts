import { type NextRequest, NextResponse } from 'next/server';
import { rootDomain } from '@/lib/utils';

function extractSubdomain(request: NextRequest): string | null {
  const url = request.url;
  const host = request.headers.get('host') || '';
  const hostname = host.split(':')[0];

  // Local development environment
  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    // Only consider it a subdomain if it has the format subdomain.localhost
    // This prevents treating localhost:3002 as having a subdomain
    if (hostname.includes('.localhost') && !hostname.match(/^localhost:\d+$/)) {
      const parts = hostname.split('.');
      if (parts.length > 1 && parts[0] !== 'localhost') {
        return parts[0];
      }
    }

    return null;
  }

  // Production environment
  const rootDomainFormatted = rootDomain.split(':')[0];

  // Handle preview deployment URLs (tenant---branch-name.vercel.app)
  if (hostname.includes('---') && hostname.endsWith('.vercel.app')) {
    const parts = hostname.split('---');
    return parts.length > 0 ? parts[0] : null;
  }

  // Regular subdomain detection
  const isSubdomain =
    hostname !== rootDomainFormatted &&
    hostname !== `www.${rootDomainFormatted}` &&
    hostname.endsWith(`.${rootDomainFormatted}`);

  return isSubdomain ? hostname.replace(`.${rootDomainFormatted}`, '') : null;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const subdomain = extractSubdomain(request);
  const url = request.nextUrl.clone();

  // Handle referral tracking
  const refCode = url.searchParams.get('ref');
  let response: NextResponse;

  // Skip subdomain processing for vendor, admin, auth, and api routes
  if (pathname.startsWith('/vendor') ||
      pathname.startsWith('/admin') ||
      pathname.startsWith('/auth') ||
      pathname.startsWith('/api') ||
      pathname.startsWith('/_next')) {
    response = NextResponse.next();
  } else if (subdomain) {
    // For the root path on a subdomain, rewrite to the subdomain page
    if (pathname === '/') {
      response = NextResponse.rewrite(new URL(`/s/${subdomain}`, request.url));
    } else {
      response = NextResponse.next();
    }
  } else {
    // On the root domain, allow normal access
    response = NextResponse.next();
  }

  // Add referral tracking cookies if ref parameter exists
  if (refCode) {
    response.cookies.set('referralCode', refCode, {
      maxAge: 24 * 60 * 60, // 24 hours
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    // Also set IP address for tracking
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ipAddress = forwardedFor?.split(',')[0] || realIp || request.ip || 'unknown';

    response.cookies.set('referralIp', ipAddress, {
      maxAge: 24 * 60 * 60, // 24 hours
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    // Set user agent
    const userAgent = request.headers.get('user-agent') || 'unknown';
    response.cookies.set('referralUserAgent', userAgent, {
      maxAge: 24 * 60 * 60, // 24 hours
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all paths except for:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. all root files inside /public (e.g. /favicon.ico)
     */
    '/((?!api|_next|[\\w-]+\\.\\w+).*)'
  ]
};
