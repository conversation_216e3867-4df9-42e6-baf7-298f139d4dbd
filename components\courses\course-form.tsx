"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Upload, Video, Image as ImageIcon, X } from "lucide-react";


interface CourseFormProps {
  vendorId: Id<"vendors">;
  course?: {
    _id: Id<"courses">;
    title: string;
    description: string;
    price: number;
    videoUrl: string;
    duration?: number;
    thumbnail?: string;
    isPublic: boolean;
    requiresPro: boolean;
  };
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CourseForm({ vendorId, course, onSuccess, onCancel }: CourseFormProps) {
  const [title, setTitle] = useState(course?.title || "");
  const [description, setDescription] = useState(course?.description || "");
  const [price, setPrice] = useState(course?.price?.toString() || "");
  const [videoUrl, setVideoUrl] = useState(course?.videoUrl || "");
  const [duration, setDuration] = useState(course?.duration?.toString() || "");
  const [thumbnail, setThumbnail] = useState(course?.thumbnail || "");
  const [isPublic, setIsPublic] = useState(course?.isPublic ?? true);
  const [requiresPro, setRequiresPro] = useState(course?.requiresPro ?? false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");

  const createCourse = useMutation(api.courses.createCourse);
  const updateCourse = useMutation(api.courses.updateCourse);

  const handleVideoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      alert("Please select a valid video file");
      return;
    }

    // Validate file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
      alert("Video file size must be less than 100MB");
      return;
    }

    setIsUploading(true);
    try {
      // In a real implementation, you would upload to Cloudinary here
      // For now, we'll simulate with a placeholder URL
      const simulatedUrl = `https://res.cloudinary.com/demo/video/upload/v1234567890/courses/${file.name.replace(/\s+/g, '_')}`;
      setVideoUrl(simulatedUrl);
      alert("Video uploaded successfully!");
    } catch (error) {
      alert("Failed to upload video");
    } finally {
      setIsUploading(false);
    }
  };

  const handleThumbnailUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert("Please select a valid image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert("Image file size must be less than 5MB");
      return;
    }

    setIsUploading(true);
    try {
      // In a real implementation, you would upload to Cloudinary here
      // For now, we'll simulate with a placeholder URL
      const simulatedUrl = `https://res.cloudinary.com/demo/image/upload/v1234567890/courses/thumbnails/${file.name.replace(/\s+/g, '_')}`;
      setThumbnail(simulatedUrl);
      alert("Thumbnail uploaded successfully!");
    } catch (error) {
      alert("Failed to upload thumbnail");
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validation
    if (!title.trim()) {
      setError("Course title is required");
      return;
    }

    if (!description.trim()) {
      setError("Course description is required");
      return;
    }

    if (!price || parseFloat(price) <= 0) {
      setError("Valid price is required");
      return;
    }

    if (!videoUrl.trim()) {
      setError("Course video is required");
      return;
    }

    try {
      if (course) {
        // Update existing course
        await updateCourse({
          courseId: course._id,
          title: title.trim(),
          description: description.trim(),
          price: parseFloat(price),
          videoUrl: videoUrl.trim(),
          duration: duration ? parseInt(duration) : undefined,
          thumbnail: thumbnail || undefined,
          isPublic,
          requiresPro,
        });
        alert("Course updated successfully!");
      } else {
        // Create new course
        await createCourse({
          vendorId,
          title: title.trim(),
          description: description.trim(),
          price: parseFloat(price),
          videoUrl: videoUrl.trim(),
          duration: duration ? parseInt(duration) : undefined,
          thumbnail: thumbnail || undefined,
          isPublic,
          requiresPro,
        });
        alert("Course created successfully!");
      }

      onSuccess?.();
    } catch (error) {
      setError("Failed to save course. Please try again.");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Course Information</CardTitle>
          <CardDescription>
            Basic information about your course
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Course Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter course title"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what students will learn in this course"
              rows={4}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="price">Price (₦) *</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            <div>
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                min="1"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                placeholder="e.g., 60"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Course Media</CardTitle>
          <CardDescription>
            Upload your course video and thumbnail
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="video">Course Video *</Label>
            <div className="space-y-2">
              <Input
                id="video"
                type="file"
                accept="video/*"
                onChange={handleVideoUpload}
                disabled={isUploading}
                className="cursor-pointer"
              />
              {videoUrl && (
                <div className="flex items-center space-x-2 p-2 bg-green-50 border border-green-200 rounded">
                  <Video className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-800">Video uploaded</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setVideoUrl("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
              <p className="text-sm text-gray-500">
                Upload your course video (max 100MB). Supported formats: MP4, MOV, AVI
              </p>
            </div>
          </div>

          <div>
            <Label htmlFor="thumbnail">Course Thumbnail</Label>
            <div className="space-y-2">
              <Input
                id="thumbnail"
                type="file"
                accept="image/*"
                onChange={handleThumbnailUpload}
                disabled={isUploading}
                className="cursor-pointer"
              />
              {thumbnail && (
                <div className="flex items-center space-x-2 p-2 bg-green-50 border border-green-200 rounded">
                  <ImageIcon className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-800">Thumbnail uploaded</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setThumbnail("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
              <p className="text-sm text-gray-500">
                Upload a thumbnail image for your course (max 5MB)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Access Settings</CardTitle>
          <CardDescription>
            Control who can access your course
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Public Course</Label>
              <p className="text-sm text-gray-500">
                Make this course visible to all users
              </p>
            </div>
            <Switch
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Requires Pro Subscription</Label>
              <p className="text-sm text-gray-500">
                Only Pro users can access this course
              </p>
            </div>
            <Switch
              checked={requiresPro}
              onCheckedChange={setRequiresPro}
            />
          </div>

          {requiresPro && (
            <div className="p-3 bg-purple-50 border border-purple-200 rounded">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  Pro Feature
                </Badge>
                <span className="text-sm text-purple-800">
                  This course will only be accessible to users with Pro subscriptions
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isUploading}>
          {isUploading ? "Uploading..." : course ? "Update Course" : "Create Course"}
        </Button>
      </div>
    </form>
  );
}
