{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.2.2", "@upstash/redis": "^1.34.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "frimousse": "^0.2.0", "lucide-react": "^0.510.0", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "@types/node": "^22.15.17", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}}