"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { useAuth } from "@/lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Send, AlertCircle } from "lucide-react";

interface ProductRequestFormProps {
  vendorId: Id<"vendors">;
  vendorName?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ProductRequestForm({ 
  vendorId, 
  vendorName, 
  onSuccess, 
  onCancel 
}: ProductRequestFormProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    productName: "",
    description: "",
    estimatedPrice: "",
    contactInfo: {
      name: user?.name || "",
      phone: user?.phone || "",
      email: "",
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const createProductRequest = useMutation(api.productRequests.createProductRequest);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      if (!formData.productName.trim()) {
        throw new Error("Product name is required");
      }

      if (!formData.description.trim()) {
        throw new Error("Product description is required");
      }

      if (!formData.contactInfo.name.trim()) {
        throw new Error("Your name is required");
      }

      if (!formData.contactInfo.phone.trim()) {
        throw new Error("Your phone number is required");
      }

      const requestData = {
        requesterId: user?._id,
        vendorId,
        productName: formData.productName.trim(),
        description: formData.description.trim(),
        estimatedPrice: formData.estimatedPrice ? parseFloat(formData.estimatedPrice) : undefined,
        contactInfo: {
          name: formData.contactInfo.name.trim(),
          phone: formData.contactInfo.phone.trim(),
          email: formData.contactInfo.email.trim() || undefined,
        },
      };

      await createProductRequest(requestData);
      setSuccess(true);
      
      // Reset form
      setFormData({
        productName: "",
        description: "",
        estimatedPrice: "",
        contactInfo: {
          name: user?.name || "",
          phone: user?.phone || "",
          email: "",
        },
      });

      setTimeout(() => {
        onSuccess?.();
      }, 2000);
    } catch (err: any) {
      setError(err.message || "Failed to submit product request");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith("contactInfo.")) {
      const contactField = field.split(".")[1];
      setFormData(prev => ({
        ...prev,
        contactInfo: {
          ...prev.contactInfo,
          [contactField]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };



  if (success) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Send className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Request Submitted Successfully!
          </h3>
          <p className="text-gray-600 mb-4">
            Your product request has been sent to {vendorName || "the vendor"}. 
            They will review it and get back to you soon.
          </p>
          <div className="flex justify-center space-x-4">
            <Button onClick={onSuccess} variant="outline">
              Close
            </Button>
            <Button onClick={() => setSuccess(false)}>
              Submit Another Request
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {/* Request Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            Product Request Details
          </CardTitle>
          <CardDescription>
            Tell {vendorName || "the vendor"} what product you're looking for
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="productName">Product Name *</Label>
            <Input
              id="productName"
              value={formData.productName}
              onChange={(e) => handleInputChange("productName", e.target.value)}
              placeholder="What product are you looking for?"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Provide detailed description of the product you need..."
              rows={4}
              required
            />
          </div>

          <div>
            <Label htmlFor="estimatedPrice">Estimated Price (₦)</Label>
            <Input
              id="estimatedPrice"
              type="number"
              min="0"
              step="0.01"
              value={formData.estimatedPrice}
              onChange={(e) => handleInputChange("estimatedPrice", e.target.value)}
              placeholder="0.00"
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>
            How should the vendor contact you about this request?
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contactName">Your Name *</Label>
              <Input
                id="contactName"
                value={formData.contactInfo.name}
                onChange={(e) => handleInputChange("contactInfo.name", e.target.value)}
                placeholder="Enter your full name"
                required
              />
            </div>

            <div>
              <Label htmlFor="contactPhone">Phone Number *</Label>
              <Input
                id="contactPhone"
                type="tel"
                value={formData.contactInfo.phone}
                onChange={(e) => handleInputChange("contactInfo.phone", e.target.value)}
                placeholder="e.g., +234 ************"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="contactEmail">Email Address (Optional)</Label>
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactInfo.email}
              onChange={(e) => handleInputChange("contactInfo.email", e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Submitting..." : "Submit Request"}
        </Button>
      </div>
    </form>
  );
}
