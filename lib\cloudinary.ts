import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export default cloudinary;

// Helper function to generate upload signature
export function generateUploadSignature(params: Record<string, any>) {
  return cloudinary.utils.api_sign_request(params, process.env.CLOUDINARY_API_SECRET!);
}

// Helper function to get optimized image URL
export function getOptimizedImageUrl(publicId: string, options?: {
  width?: number;
  height?: number;
  quality?: string;
  format?: string;
}) {
  return cloudinary.url(publicId, {
    width: options?.width,
    height: options?.height,
    quality: options?.quality || 'auto',
    format: options?.format || 'auto',
    fetch_format: 'auto',
    crop: 'fill',
  });
}

// Helper function to get video URL
export function getVideoUrl(publicId: string, options?: {
  width?: number;
  height?: number;
  quality?: string;
}) {
  return cloudinary.url(publicId, {
    resource_type: 'video',
    width: options?.width,
    height: options?.height,
    quality: options?.quality || 'auto',
    format: 'auto',
  });
}
