"use client";

import { useEffect, useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { getReferralData, clearReferralData } from '@/lib/referral-tracking';

export function useReferralTracking() {
  const [isTracking, setIsTracking] = useState(false);
  const trackReferralClick = useMutation(api.resellers.trackReferralClick);

  useEffect(() => {
    const handleReferralTracking = async () => {
      // Check if we have referral code in cookies (set by middleware)
      const referralCode = getCookie('referralCode');
      const referralIp = getCookie('referralIp');
      const referralUserAgent = getCookie('referralUserAgent');

      if (referralCode && !isTracking) {
        setIsTracking(true);
        
        try {
          // Track the referral click
          const result = await trackReferralClick({
            linkCode: referralCode,
            ipAddress: referralIp || undefined,
            userAgent: referralUserAgent || undefined,
          });

          if (result.success && result.referralLink) {
            // Store referral data in localStorage for checkout
            const referralData = {
              userId: result.referralLink.userId,
              vendorId: result.referralLink.vendorId,
              productId: result.referralLink.productId,
              commissionRate: result.referralLink.commissionRate,
            };

            localStorage.setItem('referralData', JSON.stringify(referralData));
            localStorage.setItem('referralExpiry', (Date.now() + 24 * 60 * 60 * 1000).toString());

            // Clear the cookies after successful tracking
            deleteCookie('referralCode');
            deleteCookie('referralIp');
            deleteCookie('referralUserAgent');
          }
        } catch (error) {
          console.error('Failed to track referral:', error);
        } finally {
          setIsTracking(false);
        }
      }
    };

    handleReferralTracking();
  }, [trackReferralClick, isTracking]);

  return {
    referralData: getReferralData(),
    clearReferralData,
    isTracking,
  };
}

// Helper functions for cookie management
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}

function deleteCookie(name: string): void {
  if (typeof document === 'undefined') return;
  
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}
