import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Create a new product request
export const createProductRequest = mutation({
  args: {
    requesterId: v.optional(v.id("users")),
    vendorId: v.id("vendors"),
    productName: v.string(),
    description: v.string(),
    estimatedPrice: v.optional(v.number()),
    contactInfo: v.object({
      name: v.string(),
      phone: v.string(),
      email: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    const requestId = await ctx.db.insert("productRequests", {
      requesterId: args.requesterId,
      vendorId: args.vendorId,
      productName: args.productName,
      description: args.description,
      estimatedPrice: args.estimatedPrice,
      guestName: args.contactInfo.name,
      guestPhone: args.contactInfo.phone,
      guestEmail: args.contactInfo.email,
      status: "pending",
      createdAt: now,
      updatedAt: now,
    });

    return requestId;
  },
});

// Update product request status
export const updateRequestStatus = mutation({
  args: {
    requestId: v.id("productRequests"),
    status: v.union(
      v.literal("pending"),
      v.literal("acknowledged"),
      v.literal("quoted"),
      v.literal("accepted"),
      v.literal("rejected"),
      v.literal("completed")
    ),
    vendorResponse: v.optional(v.string()),
    quotedPrice: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.vendorResponse) {
      updates.vendorResponse = args.vendorResponse;
    }

    if (args.quotedPrice) {
      updates.quotedPrice = args.quotedPrice;
    }

    await ctx.db.patch(args.requestId, updates);
    return await ctx.db.get(args.requestId);
  },
});

// Get product requests by vendor
export const getRequestsByVendor = query({
  args: { 
    vendorId: v.id("vendors"),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("productRequests")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const requests = await query.collect();
    const limitedRequests = args.limit ? requests.slice(0, args.limit) : requests;
    
    // Get requester information for each request
    const requestsWithUsers = await Promise.all(
      limitedRequests.map(async (request) => {
        let requester = null;
        if (request.requesterId) {
          requester = await ctx.db.get(request.requesterId);
        }

        return {
          ...request,
          requester,
        };
      })
    );

    return requestsWithUsers.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Get product requests by requester
export const getRequestsByRequester = query({
  args: { 
    requesterId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("productRequests")
      .withIndex("by_requester", (q) => q.eq("requesterId", args.requesterId));

    const requests = await query.collect();
    const limitedRequests = args.limit ? requests.slice(0, args.limit) : requests;
    
    // Get vendor information for each request
    const requestsWithVendors = await Promise.all(
      limitedRequests.map(async (request) => {
        const vendor = await ctx.db.get(request.vendorId);
        return {
          ...request,
          vendor,
        };
      })
    );

    return requestsWithVendors.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Get single product request
export const getProductRequest = query({
  args: { requestId: v.id("productRequests") },
  handler: async (ctx, args) => {
    const request = await ctx.db.get(args.requestId);
    if (!request) return null;

    // Get vendor information
    const vendor = await ctx.db.get(request.vendorId);
    
    // Get requester information if exists
    let requester = null;
    if (request.requesterId) {
      requester = await ctx.db.get(request.requesterId);
    }

    return {
      ...request,
      vendor,
      requester,
    };
  },
});

// Get product request statistics for vendor dashboard
export const getRequestStats = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    const requests = await ctx.db
      .query("productRequests")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId))
      .collect();

    const totalRequests = requests.length;
    const pendingRequests = requests.filter(r => r.status === "pending").length;
    const acknowledgedRequests = requests.filter(r => r.status === "acknowledged").length;
    const quotedRequests = requests.filter(r => r.status === "quoted").length;
    const acceptedRequests = requests.filter(r => r.status === "accepted").length;
    const completedRequests = requests.filter(r => r.status === "completed").length;
    const rejectedRequests = requests.filter(r => r.status === "rejected").length;

    // Get recent requests (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentRequests = requests.filter(r => r.createdAt >= thirtyDaysAgo);

    return {
      totalRequests,
      pendingRequests,
      acknowledgedRequests,
      quotedRequests,
      acceptedRequests,
      completedRequests,
      rejectedRequests,
      recentRequestsCount: recentRequests.length,
    };
  },
});

// Search product requests
export const searchRequests = query({
  args: {
    vendorId: v.id("vendors"),
    searchTerm: v.string(),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("productRequests")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    const requests = await query.collect();

    // Filter by search term and status
    const filteredRequests = requests.filter(request => {
      const matchesSearch = request.productName.toLowerCase().includes(args.searchTerm.toLowerCase()) ||
                           request.description.toLowerCase().includes(args.searchTerm.toLowerCase());

      const matchesStatus = !args.status || request.status === args.status;

      return matchesSearch && matchesStatus;
    });

    const sortedRequests = filteredRequests.sort((a, b) => b.createdAt - a.createdAt);

    // Get requester information for each request
    const requestsWithUsers = await Promise.all(
      (args.limit ? sortedRequests.slice(0, args.limit) : sortedRequests).map(async (request) => {
        let requester = null;
        if (request.requesterId) {
          requester = await ctx.db.get(request.requesterId);
        }

        return {
          ...request,
          requester,
        };
      })
    );

    return requestsWithUsers;
  },
});

// Delete product request
export const deleteProductRequest = mutation({
  args: { requestId: v.id("productRequests") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.requestId);
    return { success: true };
  },
});
