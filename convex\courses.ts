import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new course
export const createCourse = mutation({
  args: {
    vendorId: v.id("vendors"),
    productId: v.optional(v.id("products")),
    title: v.string(),
    description: v.string(),
    price: v.number(),
    videoUrl: v.string(),
    duration: v.optional(v.number()),
    thumbnail: v.optional(v.string()),
    isPublic: v.boolean(),
    requiresPro: v.boolean(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const courseId = await ctx.db.insert("courses", {
      vendorId: args.vendorId,
      productId: args.productId,
      title: args.title,
      description: args.description,
      price: args.price,
      videoUrl: args.videoUrl,
      duration: args.duration,
      thumbnail: args.thumbnail,
      isActive: true,
      isPublic: args.isPublic,
      requiresPro: args.requiresPro,
      createdAt: now,
      updatedAt: now,
    });

    return courseId;
  },
});

// Update course
export const updateCourse = mutation({
  args: {
    courseId: v.id("courses"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    videoUrl: v.optional(v.string()),
    duration: v.optional(v.number()),
    thumbnail: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    requiresPro: v.optional(v.boolean()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { courseId, ...updates } = args;
    
    await ctx.db.patch(courseId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(courseId);
  },
});

// Delete course
export const deleteCourse = mutation({
  args: { courseId: v.id("courses") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.courseId);
    return { success: true };
  },
});

// Get courses by vendor
export const getCoursesByVendor = query({
  args: {
    vendorId: v.id("vendors"),
    activeOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("courses")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    const courses = args.limit
      ? await query.take(args.limit)
      : await query.collect();

    if (args.activeOnly) {
      return courses.filter(c => c.isActive);
    }

    return courses;
  },
});

// Get course by ID with access control
export const getCourseById = query({
  args: { 
    courseId: v.id("courses"),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const course = await ctx.db.get(args.courseId);
    
    if (!course || !course.isActive) {
      return null;
    }

    // Get vendor information
    const vendor = await ctx.db.get(course.vendorId);
    
    // Check access permissions
    let hasAccess = false;
    let accessReason = "";

    if (course.isPublic && !course.requiresPro) {
      hasAccess = true;
      accessReason = "public";
    } else if (args.userId) {
      const user = await ctx.db.get(args.userId);
      
      if (user) {
        // Check if user is the vendor who created the course
        if (vendor && vendor.userId === args.userId) {
          hasAccess = true;
          accessReason = "owner";
        }
        // Check if user has Pro access for Pro-required courses
        else if (course.requiresPro && user.isPro) {
          hasAccess = true;
          accessReason = "pro";
        }
        // Check if course is public but user is logged in
        else if (course.isPublic) {
          hasAccess = true;
          accessReason = "public_user";
        }
      }
    }

    return {
      ...course,
      vendor,
      hasAccess,
      accessReason,
    };
  },
});

// Get public courses
export const getPublicCourses = query({
  args: { 
    limit: v.optional(v.number()),
    requiresPro: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("courses")
      .withIndex("by_public", (q) => q.eq("isPublic", true))
      .filter((q) => q.eq(q.field("isActive"), true));

    const courses = args.limit
      ? await query.take(args.limit)
      : await query.collect();

    // Filter by Pro requirement if specified
    let filteredCourses = courses;
    if (args.requiresPro !== undefined) {
      filteredCourses = courses.filter(c => c.requiresPro === args.requiresPro);
    }

    // Get vendor information for each course
    const coursesWithVendors = await Promise.all(
      filteredCourses.map(async (course) => {
        const vendor = await ctx.db.get(course.vendorId);
        return {
          ...course,
          vendor,
        };
      })
    );

    return coursesWithVendors;
  },
});

// Search courses
export const searchCourses = query({
  args: {
    searchTerm: v.string(),
    vendorId: v.optional(v.id("vendors")),
    requiresPro: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let courses;

    if (args.vendorId) {
      const query = ctx.db
        .query("courses")
        .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));
      courses = args.limit ? await query.take(args.limit) : await query.collect();
    } else {
      const query = ctx.db
        .query("courses")
        .withIndex("by_public", (q) => q.eq("isPublic", true))
        .filter((q) => q.eq(q.field("isActive"), true));
      courses = args.limit ? await query.take(args.limit) : await query.collect();
    }

    // Filter by search term
    const searchTermLower = args.searchTerm.toLowerCase();
    const filteredCourses = courses.filter(course =>
      course.title.toLowerCase().includes(searchTermLower) ||
      course.description.toLowerCase().includes(searchTermLower)
    );

    // Filter by Pro requirement if specified
    let finalCourses = filteredCourses;
    if (args.requiresPro !== undefined) {
      finalCourses = filteredCourses.filter(c => c.requiresPro === args.requiresPro);
    }

    // Get vendor information for each course
    const coursesWithVendors = await Promise.all(
      finalCourses.map(async (course) => {
        const vendor = await ctx.db.get(course.vendorId);
        return {
          ...course,
          vendor,
        };
      })
    );

    return coursesWithVendors;
  },
});

// Get course statistics for vendor dashboard
export const getCourseStats = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    const courses = await ctx.db
      .query("courses")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId))
      .collect();

    const totalCourses = courses.length;
    const activeCourses = courses.filter(c => c.isActive).length;
    const publicCourses = courses.filter(c => c.isPublic && c.isActive).length;
    const proCourses = courses.filter(c => c.requiresPro && c.isActive).length;

    return {
      totalCourses,
      activeCourses,
      publicCourses,
      proCourses,
    };
  },
});

// Toggle course active status
export const toggleCourseStatus = mutation({
  args: { courseId: v.id("courses") },
  handler: async (ctx, args) => {
    const course = await ctx.db.get(args.courseId);
    if (!course) {
      throw new Error("Course not found");
    }

    await ctx.db.patch(args.courseId, {
      isActive: !course.isActive,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.courseId);
  },
});
