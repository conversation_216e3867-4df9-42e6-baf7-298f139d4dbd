**Product Requirements Document (PRD)**

---

**Project Title:** Multi-Tenant Vendor Shop Platform

**Prepared For:** AI Development Team

**Prepared By:** <PERSON><PERSON><PERSON> (samboy)

**Goal:** Empower small and large businesses in Africa to create digital shops with dedicated subdomains, enabling online sales, WhatsApp-based checkout, wallet-based payments, reseller systems, and course uploads.

---

## 1. Overview

The Multi-Tenant Vendor Shop Platform allows vendors to register, get a unique subdomain, list and manage products, chat with users, and sell online through WhatsApp and a wallet system. It includes a powerful admin panel, vendor dashboards, resale system, course uploads, and more.

---

## 2. Key Roles & User Types

### 1. **Visitor/User (Unauthenticated)**

* View vendor shop public page
* View product details
* Click "Buy via WhatsApp"
* Request product

### 2. **Registered User / Buyer**

* Wallet balance
* Purchase products
* Request products
* Chat with vendors
* Buy courses (if Pro)

### 3. **Reseller**

* Access resale link generator
* Earn commission on vendor sales
* Chat with vendors
* Withdraw to wallet

### 4. **Vendor**

* Gets subdomain
* Manages shop in dashboard
* Adds/edit/delete products & courses
* Chat with users/resellers
* Sets WhatsApp number per product
* Manages wallet

### 5. **Admin**

* Manage users/vendors/resellers
* Approve payments/withdrawals
* Monitor orders, chats, activities
* Set commission percentage
* Create platform-wide announcements

---

## 3. Pages & Functionalities

### 3.1 Landing Page (Main Domain)

* Hero section
* Features
* How it works
* Register/Login CTA
* Optional: Top vendors

### 3.2 Auth Pages

#### Login (Phone number only)

* Verify phone via OTP

#### Register

* Name
* Phone Number
* Address
* WhatsApp Number (optional)

### 3.3 User Dashboard (Main Domain)

* View profile
* Fund wallet (via Paystack)
* View transaction history
* Purchase history
* Request product
* Upgrade to Pro (to access courses)

### 3.4 Vendor Dashboard (Subdomain)

Route: `vendorname.domain.com/dashboard`

* Overview (stats, sales, chats)
* Profile Settings
* Products

  * Create / Edit / Delete
  * Set price, description, image
  * Set WhatsApp number or use default
* Courses

  * Upload video (Pro only)
  * Manage access
* Wallet

  * Earnings
  * Withdraw to bank
  * Send to reseller
* Reseller System

  * Generate resale links
  * Track referrals
* Chat

  * With resellers / users / collaborators
* Product Requests

  * View incoming requests from users

### 3.5 Vendor Public Page (Subdomain)

Route: `vendorname.domain.com`

* Vendor Profile
* Products
* "Buy via WhatsApp" button
* Request Product button
* Courses (if public)

### 3.6 Resale Page

Route: `domain.com/resale/vendorname/product-id`

* Product preview
* Vendor info
* Buy on WhatsApp
* Tracks reseller ID for commission

### 3.7 Admin Dashboard

Route: `admin.domain.com`

* Login (email + password)
* Dashboard Overview
* Manage Vendors

  * Suspend, verify, upgrade to Pro
* Manage Users & Resellers

  * View activity, suspend
* Wallet Management

  * Approve withdrawals
  * View transactions
* Commission Settings

  * Set global % (default 2%)
* Reports

  * Sales, most active vendors, top resellers
* Chat Monitor (view flagged messages)
* CMS Pages (manage landing content)

---

## 4. Technical Stack

* Frontend: **Next.js (App Router)**
* UI: **ShadCN UI**
* Authentication: **Phone number OTP (Clerk / custom)**
* Payment Gateway: **Paystack**
* Database: **PostgreSQL (via Prisma or Supabase)**
* Hosting: **Vercel**
* Subdomain handling: **Middleware + wildcard subdomain DNS**
* Chat: **Real-time using Pusher / WebSockets / Supabase Realtime**

---

## 5. Monetization

* 2% commission on all sales
* Vendors can upgrade to Pro for uploading video courses
* Wallet system for internal transactions

---

## 6. Notifications

* Email and/or SMS for key events (order received, withdrawal request, new chat, etc.)
* Dashboard alerts

---

## 7. Future Features (Phase 2+)

* Vendor QR Code
* Affiliate dashboard for resellers
* Stripe / Flutterwave support
* Push notifications
* Vendor review system

---

## 8. Security & Compliance

* Data privacy for users and vendors
* OTP-based secure login
* Input validation and XSS/CSRF protection

---

## 9. Timeline

* Phase 1 (MVP): Vendor onboarding, public shop, product listing, WhatsApp checkout
* Phase 2: Wallet system, reseller flow, chat system, admin panel
* Phase 3: Pro features, course access, analytics, push notifications

---

## 10. Success Metrics

* Number of vendors onboarded
* Total sales processed
* Number of resellers and resale link usage
* Daily active users and chats
* User satisfaction ratings

---

*This PRD is to be followed by wireframes, UI/UX flows, and feature-by-feature breakdown for development.*
