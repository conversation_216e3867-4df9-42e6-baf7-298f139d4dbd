/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as courses from "../courses.js";
import type * as orders from "../orders.js";
import type * as paystack from "../paystack.js";
import type * as productRequests from "../productRequests.js";
import type * as products from "../products.js";
import type * as resellers from "../resellers.js";
import type * as users from "../users.js";
import type * as vendors from "../vendors.js";
import type * as wallet from "../wallet.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  courses: typeof courses;
  orders: typeof orders;
  paystack: typeof paystack;
  productRequests: typeof productRequests;
  products: typeof products;
  resellers: typeof resellers;
  users: typeof users;
  vendors: typeof vendors;
  wallet: typeof wallet;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
