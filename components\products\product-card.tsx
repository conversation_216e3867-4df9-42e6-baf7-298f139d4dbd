"use client";

import { useState } from "react";
import { useCart } from "@/lib/cart-context";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Eye, 
  Heart,
  Share2,
  AlertCircle
} from "lucide-react";

interface ProductCardProps {
  product: {
    _id: string;
    name: string;
    description: string;
    price: number;
    compareAtPrice?: number;
    images: string[];
    category?: string;
    tags?: string[];
    stockQuantity?: number;
    isUnlimitedStock: boolean;
    isActive: boolean;
    isDigital: boolean;
    vendorId: string;
    vendor?: {
      _id: string;
      shopName: string;
      subdomain: string;
    };
  };
  onViewProduct?: (product: any) => void;
  showVendorInfo?: boolean;
  className?: string;
}

export function ProductCard({ 
  product, 
  onViewProduct, 
  showVendorInfo = true,
  className = "" 
}: ProductCardProps) {
  const { addItem, openCart } = useCart();
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
    }).format(price);
  };

  const getStockStatus = () => {
    if (product.isUnlimitedStock) {
      return { status: "unlimited", color: "bg-green-100 text-green-800", text: "In Stock" };
    }
    
    const stock = product.stockQuantity || 0;
    if (stock === 0) {
      return { status: "out", color: "bg-red-100 text-red-800", text: "Out of Stock" };
    } else if (stock <= 5) {
      return { status: "low", color: "bg-yellow-100 text-yellow-800", text: `Only ${stock} left` };
    } else {
      return { status: "good", color: "bg-green-100 text-green-800", text: "In Stock" };
    }
  };

  const stockStatus = getStockStatus();
  const isOutOfStock = stockStatus.status === "out";
  const maxQuantity = product.isUnlimitedStock ? 99 : (product.stockQuantity || 1);

  const handleAddToCart = async () => {
    if (isOutOfStock || !product.isActive) return;

    setIsAddingToCart(true);
    
    try {
      addItem({
        productId: product._id as any,
        productName: product.name,
        price: product.price,
        quantity: quantity,
        image: product.images[0],
        vendorId: product.vendorId as any,
        vendorName: product.vendor?.shopName,
        maxStock: product.stockQuantity,
        isUnlimitedStock: product.isUnlimitedStock,
      });

      // Open cart after adding item
      setTimeout(() => {
        openCart();
      }, 100);
    } catch (error) {
      console.error("Failed to add item to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    const validQuantity = Math.max(1, Math.min(newQuantity, maxQuantity));
    setQuantity(validQuantity);
  };

  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price
    ? Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)
    : 0;

  return (
    <Card className={`overflow-hidden hover:shadow-lg transition-shadow ${className}`}>
      <div className="aspect-square relative group">
        <img
          src={product.images[0] || "/placeholder-product.jpg"}
          alt={product.name}
          className="w-full h-full object-cover"
        />
        
        {/* Overlay actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onViewProduct?.(product)}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0"
            >
              <Heart className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0"
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Status badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {!product.isActive && (
            <Badge variant="secondary" className="bg-gray-100 text-gray-800">
              Inactive
            </Badge>
          )}
          {discountPercentage > 0 && (
            <Badge variant="destructive">
              -{discountPercentage}%
            </Badge>
          )}
          {product.isDigital && (
            <Badge variant="outline" className="bg-blue-100 text-blue-800">
              Digital
            </Badge>
          )}
        </div>

        <div className="absolute top-2 right-2">
          <Badge className={stockStatus.color}>
            {stockStatus.text}
          </Badge>
        </div>
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Vendor info */}
          {showVendorInfo && product.vendor && (
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {product.vendor.shopName}
              </Badge>
            </div>
          )}

          {/* Product info */}
          <div>
            <h3 className="font-semibold text-lg line-clamp-1 mb-1">{product.name}</h3>
            <p className="text-gray-600 text-sm line-clamp-2 mb-2">{product.description}</p>
            
            {/* Price */}
            <div className="flex items-center space-x-2 mb-2">
              <span className="font-bold text-lg">{formatPrice(product.price)}</span>
              {product.compareAtPrice && product.compareAtPrice > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.compareAtPrice)}
                </span>
              )}
            </div>

            {/* Category and tags */}
            <div className="flex flex-wrap gap-1 mb-3">
              {product.category && (
                <Badge variant="outline" className="text-xs">
                  {product.category}
                </Badge>
              )}
              {product.tags && product.tags.slice(0, 2).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {product.tags && product.tags.length > 2 && (
                <Badge variant="secondary" className="text-xs">
                  +{product.tags.length - 2}
                </Badge>
              )}
            </div>
          </div>

          {/* Add to cart section */}
          {product.isActive && (
            <div className="space-y-2">
              {!isOutOfStock && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  
                  <Input
                    type="number"
                    min="1"
                    max={maxQuantity}
                    value={quantity}
                    onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                    className="h-8 w-16 text-center"
                  />
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= maxQuantity}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              )}

              <Button
                onClick={handleAddToCart}
                disabled={isOutOfStock || isAddingToCart || !product.isActive}
                className="w-full"
                size="sm"
              >
                {isAddingToCart ? (
                  "Adding..."
                ) : isOutOfStock ? (
                  <>
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Out of Stock
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </>
                )}
              </Button>
            </div>
          )}

          {!product.isActive && (
            <div className="text-center py-2">
              <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                Product Unavailable
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
