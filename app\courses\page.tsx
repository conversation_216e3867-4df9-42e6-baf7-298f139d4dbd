"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ProUpgrade } from "@/components/pro-upgrade";
import { Video, Search, Clock, DollarSign, User, Lock, Crown, Play } from "lucide-react";
import { Id } from "@/convex/_generated/dataModel";
import Link from "next/link";

export default function CoursesPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCourse, setSelectedCourse] = useState<any>(null);

  // Get public courses
  const courses = useQuery(api.courses.getPublicCourses, { limit: 50 });

  // Search courses if search term is provided
  const searchResults = useQuery(
    api.courses.searchCourses,
    searchTerm.trim() ? { searchTerm: searchTerm.trim(), limit: 20 } : "skip"
  );

  // Get course details for selected course
  const courseDetails = useQuery(
    api.courses.getCourseById,
    selectedCourse ? { 
      courseId: selectedCourse._id, 
      userId: user?._id 
    } : "skip"
  );

  const displayCourses = searchTerm.trim() ? searchResults : courses;

  const handleCourseClick = (course: any) => {
    setSelectedCourse(course);
  };

  const canAccessCourse = (course: any) => {
    if (!course.requiresPro) return true;
    return user?.isPro || false;
  };

  const getAccessBadge = (course: any) => {
    if (!course.requiresPro) {
      return <Badge variant="outline" className="bg-green-50 text-green-700">Free Access</Badge>;
    }
    
    if (user?.isPro) {
      return <Badge variant="outline" className="bg-purple-50 text-purple-700">Pro Access</Badge>;
    }
    
    return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Pro Required</Badge>;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Educational Courses</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Learn from expert vendors and expand your knowledge with our comprehensive course library
        </p>
      </div>

      {/* Search Bar */}
      <div className="max-w-md mx-auto mb-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search courses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Pro Upgrade Banner */}
      {user && !user.isPro && (
        <Card className="mb-8 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Crown className="h-8 w-8 text-purple-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Upgrade to Pro</h3>
                  <p className="text-gray-600">
                    Get unlimited access to all premium courses and exclusive content
                  </p>
                </div>
              </div>
              <ProUpgrade />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {displayCourses && displayCourses.length > 0 ? (
          displayCourses.map((course) => (
            <Card key={course._id} className="hover:shadow-lg transition-shadow cursor-pointer">
              <div onClick={() => handleCourseClick(course)}>
                {/* Course Thumbnail */}
                <div className="aspect-video bg-gray-100 rounded-t-lg relative overflow-hidden">
                  {course.thumbnail ? (
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Video className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-center justify-center">
                    <Play className="h-8 w-8 text-white opacity-0 hover:opacity-100 transition-opacity" />
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-lg text-gray-900 line-clamp-2">
                      {course.title}
                    </h3>
                    {!canAccessCourse(course) && (
                      <Lock className="h-4 w-4 text-yellow-600 flex-shrink-0 ml-2" />
                    )}
                  </div>

                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {course.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-1" />
                      {course.vendor?.shopName || "Unknown Vendor"}
                    </div>
                    {course.duration && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {course.duration} min
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-semibold text-green-600">
                        ₦{course.price.toLocaleString()}
                      </span>
                    </div>
                    {getAccessBadge(course)}
                  </div>
                </CardContent>
              </div>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? "No courses found" : "No courses available"}
            </h3>
            <p className="text-gray-600">
              {searchTerm 
                ? "Try adjusting your search terms" 
                : "Check back later for new courses"
              }
            </p>
          </div>
        )}
      </div>

      {/* Course Details Dialog */}
      {selectedCourse && (
        <Dialog open={!!selectedCourse} onOpenChange={() => setSelectedCourse(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-xl">{selectedCourse.title}</DialogTitle>
              <DialogDescription>
                Course by {selectedCourse.vendor?.shopName || "Unknown Vendor"}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Course Thumbnail */}
              {selectedCourse.thumbnail && (
                <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={selectedCourse.thumbnail}
                    alt={selectedCourse.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Course Info */}
              <div className="space-y-3">
                <p className="text-gray-700">{selectedCourse.description}</p>

                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-green-600 mr-1" />
                    <span className="font-semibold text-green-600">
                      ₦{selectedCourse.price.toLocaleString()}
                    </span>
                  </div>
                  {selectedCourse.duration && (
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-gray-500 mr-1" />
                      {selectedCourse.duration} minutes
                    </div>
                  )}
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-500 mr-1" />
                    {selectedCourse.vendor?.shopName || "Unknown Vendor"}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {getAccessBadge(selectedCourse)}
                  {selectedCourse.isPublic && (
                    <Badge variant="outline">Public</Badge>
                  )}
                </div>
              </div>

              {/* Access Control */}
              <div className="border-t pt-4">
                {canAccessCourse(selectedCourse) ? (
                  <div className="space-y-3">
                    <p className="text-green-700 font-medium">✓ You have access to this course</p>
                    <Link href={`/courses/${selectedCourse._id}`}>
                      <Button className="w-full">
                        <Play className="h-4 w-4 mr-2" />
                        Start Learning
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-yellow-700">
                      <Lock className="h-4 w-4" />
                      <span className="font-medium">Pro subscription required</span>
                    </div>
                    <p className="text-gray-600 text-sm">
                      This course requires a Pro subscription to access. Upgrade your account to unlock all premium content.
                    </p>
                    <ProUpgrade
                      trigger={
                        <Button className="w-full bg-purple-600 hover:bg-purple-700">
                          <Crown className="h-4 w-4 mr-2" />
                          Upgrade to Pro
                        </Button>
                      }
                    />
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
