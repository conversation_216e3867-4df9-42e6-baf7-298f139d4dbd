"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ProductForm } from "@/components/products/product-form";
import { ProductList } from "@/components/products/product-list";
import { Plus, ArrowLeft, Package } from "lucide-react";

export default function VendorProductsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [currentView, setCurrentView] = useState<"list" | "create" | "edit">("list");
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // Get vendor data
  const vendor = useQuery(
    api.vendors.getVendorByUserId,
    user ? { userId: user._id } : "skip"
  );

  // Handle redirects in useEffect to avoid render-time navigation
  useEffect(() => {
    if (user === null) {
      // User is not authenticated
      router.push("/auth/login");
    } else if (user && user.role !== "vendor") {
      // User is authenticated but not a vendor
      router.push("/dashboard");
    } else if (user && !vendor && vendor !== undefined) {
      // User is a vendor but doesn't have a vendor shop yet
      router.push("/vendor/setup");
    }
  }, [user, vendor, router]);

  // Show loading while checking authentication or vendor data
  if (user === undefined || vendor === undefined) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your products...</p>
        </div>
      </div>
    );
  }

  // Don't render if user is not authenticated, not a vendor, or doesn't have a vendor shop
  if (!user || user.role !== "vendor" || !vendor) {
    return null;
  }

  const handleCreateProduct = () => {
    setSelectedProduct(null);
    setCurrentView("create");
  };

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product);
    setCurrentView("edit");
  };

  const handleViewProduct = (product: any) => {
    // Navigate to product view page (to be implemented)
    router.push(`/vendor/products/${product._id}`);
  };

  const handleFormSuccess = () => {
    setCurrentView("list");
    setSelectedProduct(null);
  };

  const handleFormCancel = () => {
    setCurrentView("list");
    setSelectedProduct(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {currentView !== "list" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentView("list")}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Products
                </Button>
              )}
              
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {currentView === "list" && "Products"}
                  {currentView === "create" && "Add New Product"}
                  {currentView === "edit" && "Edit Product"}
                </h1>
                <p className="text-gray-600 mt-1">
                  {currentView === "list" && "Manage your product catalog"}
                  {currentView === "create" && "Create a new product for your store"}
                  {currentView === "edit" && "Update product information"}
                </p>
              </div>
            </div>

            {currentView === "list" && (
              <Button onClick={handleCreateProduct}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        {currentView === "list" && (
          <ProductList
            vendorId={vendor._id}
            onEditProduct={handleEditProduct}
            onViewProduct={handleViewProduct}
          />
        )}

        {(currentView === "create" || currentView === "edit") && (
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>
                    {currentView === "create" ? "Create New Product" : "Edit Product"}
                  </span>
                </CardTitle>
                <CardDescription>
                  {currentView === "create" 
                    ? "Fill in the details below to add a new product to your store."
                    : "Update the product information below."
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProductForm
                  vendorId={vendor._id}
                  product={selectedProduct}
                  onSuccess={handleFormSuccess}
                  onCancel={handleFormCancel}
                />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
