import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";

// Initialize Paystack payment
export const initializePayment = action({
  args: {
    userId: v.id("users"),
    amount: v.number(), // Amount in kobo (multiply by 100)
    email: v.string(),
    orderId: v.optional(v.id("orders")),
    purpose: v.string(), // "wallet_funding", "order_payment", etc.
  },
  handler: async (ctx, args) => {
    // In a real implementation, you would call Paystack API here
    // For now, we'll simulate the response
    
    const reference = `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Store payment initialization in database
    const paymentId = await ctx.runMutation(api.paystack.createPaymentRecord, {
      userId: args.userId,
      amount: args.amount,
      email: args.email,
      reference,
      orderId: args.orderId,
      purpose: args.purpose,
      status: "pending",
    });

    // Simulate Paystack response
    return {
      status: true,
      message: "Authorization URL created",
      data: {
        authorization_url: `https://checkout.paystack.com/${reference}`,
        access_code: `access_${reference}`,
        reference,
      },
      paymentId,
    };
  },
});

// Create payment record
export const createPaymentRecord = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    email: v.string(),
    reference: v.string(),
    orderId: v.optional(v.id("orders")),
    purpose: v.string(),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    const paymentId = await ctx.db.insert("paystackPayments", {
      userId: args.userId,
      amount: args.amount,
      email: args.email,
      reference: args.reference,
      orderId: args.orderId,
      purpose: args.purpose,
      status: args.status,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return paymentId;
  },
});

// Verify payment (webhook handler)
export const verifyPayment = action({
  args: {
    reference: v.string(),
    status: v.string(),
    amount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Get payment record
    const payment = await ctx.runQuery(api.paystack.getPaymentByReference, {
      reference: args.reference,
    });

    if (!payment) {
      throw new Error("Payment record not found");
    }

    // Update payment status
    await ctx.runMutation(api.paystack.updatePaymentStatus, {
      paymentId: payment._id,
      status: args.status,
      verifiedAmount: args.amount,
    });

    // If payment is successful, process based on purpose
    if (args.status === "success") {
      if (payment.purpose === "wallet_funding") {
        // Credit user's wallet
        await ctx.runMutation(api.wallet.creditWallet, {
          userId: payment.userId,
          amount: payment.amount / 100, // Convert from kobo to naira
          description: `Wallet funding via Paystack - ${payment.reference}`,
          reference: payment.reference,
        });
      } else if (payment.purpose === "order_payment" && payment.orderId) {
        // Update order payment status
        await ctx.runMutation(api.orders.updatePaymentStatus, {
          orderId: payment.orderId,
          paymentStatus: "paid",
        });

        // Process order (update stock, create commission, etc.)
        await ctx.runMutation(api.orders.processOrderPayment, {
          orderId: payment.orderId,
          paymentReference: payment.reference,
        });
      }
    }

    return {
      success: true,
      payment,
      processed: args.status === "success",
    };
  },
});

// Get payment by reference
export const getPaymentByReference = query({
  args: { reference: v.string() },
  handler: async (ctx, args) => {
    const payment = await ctx.db
      .query("paystackPayments")
      .withIndex("by_reference", (q) => q.eq("reference", args.reference))
      .first();

    return payment;
  },
});

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    paymentId: v.id("paystackPayments"),
    status: v.string(),
    verifiedAmount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.verifiedAmount) {
      updates.verifiedAmount = args.verifiedAmount;
    }

    await ctx.db.patch(args.paymentId, updates);
    return await ctx.db.get(args.paymentId);
  },
});

// Get user's payment history
export const getUserPayments = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
    purpose: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("paystackPayments")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.purpose) {
      query = query.filter((q) => q.eq(q.field("purpose"), args.purpose));
    }

    const payments = await query.collect();
    const sortedPayments = payments.sort((a, b) => b.createdAt - a.createdAt);
    
    return args.limit ? sortedPayments.slice(0, args.limit) : sortedPayments;
  },
});

// Get payment statistics
export const getPaymentStats = query({
  args: { 
    userId: v.optional(v.id("users")),
    vendorId: v.optional(v.id("vendors")),
  },
  handler: async (ctx, args) => {
    let query;

    if (args.userId) {
      const userId = args.userId;
      query = ctx.db.query("paystackPayments")
        .withIndex("by_user", (q) => q.eq("userId", userId));
    } else {
      query = ctx.db.query("paystackPayments");
    }

    const payments = await query.collect();

    const successfulPayments = payments.filter(p => p.status === "success");
    const failedPayments = payments.filter(p => p.status === "failed");
    const pendingPayments = payments.filter(p => p.status === "pending");

    const totalAmount = successfulPayments.reduce((sum, p) => sum + p.amount, 0);
    const walletFunding = successfulPayments
      .filter(p => p.purpose === "wallet_funding")
      .reduce((sum, p) => sum + p.amount, 0);
    const orderPayments = successfulPayments
      .filter(p => p.purpose === "order_payment")
      .reduce((sum, p) => sum + p.amount, 0);

    // Get recent payments (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentPayments = payments.filter(p => p.createdAt >= thirtyDaysAgo);

    return {
      totalPayments: payments.length,
      successfulPayments: successfulPayments.length,
      failedPayments: failedPayments.length,
      pendingPayments: pendingPayments.length,
      totalAmount: totalAmount / 100, // Convert from kobo to naira
      walletFunding: walletFunding / 100,
      orderPayments: orderPayments / 100,
      recentPaymentsCount: recentPayments.length,
      successRate: payments.length > 0 ? (successfulPayments.length / payments.length) * 100 : 0,
    };
  },
});
