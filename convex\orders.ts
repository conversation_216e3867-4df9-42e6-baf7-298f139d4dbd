import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { addCommission } from "./wallet";
import { api, internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Create a new order
export const createOrder = mutation({
  args: {
    buyerId: v.optional(v.id("users")),
    vendorId: v.id("vendors"),
    referrerId: v.optional(v.id("users")),
    items: v.array(v.object({
      productId: v.id("products"),
      productName: v.string(),
      quantity: v.number(),
      price: v.number(),
      totalPrice: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    shipping: v.optional(v.number()),
    discount: v.optional(v.number()),
    total: v.number(),
    paymentMethod: v.union(
      v.literal("whatsapp"),
      v.literal("wallet"),
      v.literal("paystack")
    ),
    shippingAddress: v.optional(v.object({
      name: v.string(),
      phone: v.string(),
      address: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
    })),
    commissionRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Generate unique order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    
    // Calculate commission amount if referrer exists
    let commissionAmount = 0;
    if (args.referrerId && args.commissionRate) {
      commissionAmount = args.total * (args.commissionRate / 100);
    }

    const now = Date.now();
    
    const orderId = await ctx.db.insert("orders", {
      buyerId: args.buyerId,
      vendorId: args.vendorId,
      referrerId: args.referrerId,
      orderNumber,
      status: "pending",
      items: args.items,
      subtotal: args.subtotal,
      tax: args.tax,
      shipping: args.shipping,
      discount: args.discount,
      total: args.total,
      paymentMethod: args.paymentMethod,
      paymentStatus: "pending",
      shippingAddress: args.shippingAddress,
      commissionRate: args.commissionRate,
      commissionAmount: commissionAmount > 0 ? commissionAmount : undefined,
      createdAt: now,
      updatedAt: now,
    });

    // Update product stock for non-unlimited stock products
    for (const item of args.items) {
      const product = await ctx.db.get(item.productId);
      if (product && !product.isUnlimitedStock && product.stockQuantity !== undefined) {
        const newStock = Math.max(0, product.stockQuantity - item.quantity);
        await ctx.db.patch(item.productId, {
          stockQuantity: newStock,
          updatedAt: now,
        });
      }
    }

    return { orderId, orderNumber };
  },
});

// Update order status
export const updateOrderStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("processing"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("refunded")
    ),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.orderId, {
      status: args.status,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.orderId);
  },
});

// Get orders by vendor
export const getOrdersByVendor = query({
  args: { 
    vendorId: v.id("vendors"),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("orders")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const orders = await query.collect();
    const limitedOrders = args.limit ? orders.slice(0, args.limit) : orders;
    return limitedOrders.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Get orders by buyer
export const getOrdersByBuyer = query({
  args: { 
    buyerId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("orders")
      .withIndex("by_buyer", (q) => q.eq("buyerId", args.buyerId));

    const orders = await query.collect();
    const limitedOrders = args.limit ? orders.slice(0, args.limit) : orders;
    return limitedOrders.sort((a, b) => b.createdAt - a.createdAt);
  },
});

// Get single order by ID
export const getOrder = query({
  args: { orderId: v.id("orders") },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId);
    if (!order) return null;

    // Get vendor information
    const vendor = await ctx.db.get(order.vendorId);
    
    // Get buyer information if exists
    let buyer = null;
    if (order.buyerId) {
      buyer = await ctx.db.get(order.buyerId);
    }

    // Get referrer information if exists
    let referrer = null;
    if (order.referrerId) {
      referrer = await ctx.db.get(order.referrerId);
    }

    return {
      ...order,
      vendor,
      buyer,
      referrer,
    };
  },
});

// Get order by order number
export const getOrderByNumber = query({
  args: { orderNumber: v.string() },
  handler: async (ctx, args) => {
    const order = await ctx.db
      .query("orders")
      .withIndex("by_order_number", (q) => q.eq("orderNumber", args.orderNumber))
      .first();

    if (!order) return null;

    // Get vendor information
    const vendor = await ctx.db.get(order.vendorId);
    
    // Get buyer information if exists
    let buyer = null;
    if (order.buyerId) {
      buyer = await ctx.db.get(order.buyerId);
    }

    return {
      ...order,
      vendor,
      buyer,
    };
  },
});

// Get order statistics for vendor dashboard
export const getOrderStats = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId))
      .collect();

    const totalOrders = orders.length;
    const pendingOrders = orders.filter(o => o.status === "pending").length;
    const completedOrders = orders.filter(o => o.status === "delivered").length;
    const cancelledOrders = orders.filter(o => o.status === "cancelled").length;
    
    const totalRevenue = orders
      .filter(o => o.paymentStatus === "paid")
      .reduce((sum, order) => sum + order.total, 0);

    // Get recent orders (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentOrders = orders.filter(o => o.createdAt >= thirtyDaysAgo);
    const recentRevenue = recentOrders
      .filter(o => o.paymentStatus === "paid")
      .reduce((sum, order) => sum + order.total, 0);

    return {
      totalOrders,
      pendingOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      recentRevenue,
      recentOrdersCount: recentOrders.length,
    };
  },
});

// Cancel order
export const cancelOrder = mutation({
  args: { 
    orderId: v.id("orders"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    if (order.status === "delivered" || order.status === "cancelled") {
      throw new Error("Cannot cancel this order");
    }

    // Restore product stock
    for (const item of order.items) {
      const product = await ctx.db.get(item.productId);
      if (product && !product.isUnlimitedStock && product.stockQuantity !== undefined) {
        await ctx.db.patch(item.productId, {
          stockQuantity: product.stockQuantity + item.quantity,
          updatedAt: Date.now(),
        });
      }
    }

    await ctx.db.patch(args.orderId, {
      status: "cancelled",
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.orderId);
  },
});

// Update payment status
export const updatePaymentStatus = mutation({
  args: {
    orderId: v.id("orders"),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("paid"),
      v.literal("failed"),
      v.literal("refunded")
    ),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    await ctx.db.patch(args.orderId, {
      paymentStatus: args.paymentStatus,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.orderId);
  },
});

// Process order payment (called after successful payment)
export const processOrderPayment = mutation({
  args: {
    orderId: v.id("orders"),
    paymentReference: v.string(),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db.get(args.orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Update order status to confirmed
    await ctx.db.patch(args.orderId, {
      status: "confirmed",
      paymentStatus: "paid",
      paymentReference: args.paymentReference,
      updatedAt: Date.now(),
    });

    // Process commission if this is a referral order
    if (order.referrerId) {
      const commissionAmount = order.total * 0.1; // 10% commission

      // Add commission to referrer's wallet
      await ctx.runMutation(api.wallet.addCommission, {
        userId: order.referrerId,
        amount: commissionAmount,
        description: `Commission from order #${order._id.slice(-8)}`,
        orderId: order._id,
      });
    }

    return await ctx.db.get(args.orderId);
  },
});
