"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRequireAuth } from "@/hooks/useRequireAuth";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  TrendingUp, 
  Link as LinkIcon, 
  Eye, 
  ShoppingCart, 
  DollarSign, 
  Users, 
  Copy,
  ExternalLink,
  ToggleLeft,
  ToggleRight,
  Trash2,
  Plus
} from "lucide-react";
import { toast } from "sonner";

export default function ResellerDashboard() {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const { user } = useAuth();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState("");
  const [selectedProduct, setSelectedProduct] = useState("");
  const [customMessage, setCustomMessage] = useState("");

  // Queries
  const resellerStats = useQuery(
    api.resellers.getResellerStats,
    user ? { userId: user._id } : "skip"
  );
  
  const referralLinks = useQuery(
    api.resellers.getUserReferralLinks,
    user ? { userId: user._id } : "skip"
  );

  const topPerformingLinks = useQuery(
    api.resellers.getTopPerformingLinks,
    user ? { userId: user._id, limit: 5 } : "skip"
  );

  const vendors = useQuery(api.vendors.getAllVendors);
  const products = useQuery(
    api.products.getProductsByVendor,
    selectedVendor ? { vendorId: selectedVendor as any } : "skip"
  );

  // Mutations
  const activateReseller = useMutation(api.resellers.activateReseller);
  const generateReferralLink = useMutation(api.resellers.generateReferralLink);
  const toggleLinkStatus = useMutation(api.resellers.toggleReferralLinkStatus);
  const deleteLink = useMutation(api.resellers.deleteReferralLink);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return null;
  }

  const handleActivateReseller = async () => {
    try {
      await activateReseller({ userId: user._id });
      toast.success("Reseller account activated!");
    } catch (error) {
      toast.error("Failed to activate reseller account");
    }
  };

  const handleGenerateLink = async () => {
    if (!selectedVendor) {
      toast.error("Please select a vendor");
      return;
    }

    try {
      const result = await generateReferralLink({
        userId: user._id,
        vendorId: selectedVendor as any,
        productId: selectedProduct ? (selectedProduct as any) : undefined,
        customMessage: customMessage || undefined,
      });

      toast.success("Referral link generated successfully!");
      setIsCreateDialogOpen(false);
      setSelectedVendor("");
      setSelectedProduct("");
      setCustomMessage("");
    } catch (error) {
      toast.error("Failed to generate referral link");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Link copied to clipboard!");
  };

  const handleToggleStatus = async (linkId: string, currentStatus: boolean) => {
    try {
      await toggleLinkStatus({ linkId: linkId as any, userId: user._id });
      toast.success(`Link ${currentStatus ? 'deactivated' : 'activated'} successfully!`);
    } catch (error) {
      toast.error("Failed to update link status");
    }
  };

  const handleDeleteLink = async (linkId: string) => {
    if (!confirm("Are you sure you want to delete this referral link?")) return;
    
    try {
      await deleteLink({ linkId: linkId as any, userId: user._id });
      toast.success("Referral link deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete referral link");
    }
  };

  // Check if user is already a reseller (has earnings/sales data)
  const isReseller = resellerStats && (
    (resellerStats.totalEarnings !== undefined && resellerStats.totalEarnings >= 0) ||
    (resellerStats.totalSales !== undefined && resellerStats.totalSales >= 0)
  );

  if (!isReseller) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <CardTitle className="text-2xl">Become a Reseller</CardTitle>
            <CardDescription>
              Start earning commissions by promoting products from our vendors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <span className="text-sm">Generate referral links for any product</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <span className="text-sm">Earn 10% commission on every sale</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <span className="text-sm">Track your performance and earnings</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <span className="text-sm">Withdraw earnings to your wallet</span>
              </div>
            </div>
            <Button 
              onClick={handleActivateReseller} 
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              Activate Reseller Account
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Reseller Dashboard</h1>
              <p className="text-gray-600 mt-1">Manage your referral links and track earnings</p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Link
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Generate Referral Link</DialogTitle>
                  <DialogDescription>
                    Create a referral link for a vendor or specific product
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="vendor">Vendor *</Label>
                    <Select value={selectedVendor} onValueChange={setSelectedVendor}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a vendor" />
                      </SelectTrigger>
                      <SelectContent>
                        {vendors?.map((vendor) => (
                          <SelectItem key={vendor._id} value={vendor._id}>
                            {vendor.emoji} {vendor.shopName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {selectedVendor && (
                    <div>
                      <Label htmlFor="product">Product (Optional)</Label>
                      <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a product or leave empty for shop link" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Entire Shop</SelectItem>
                          {products?.map((product) => (
                            <SelectItem key={product._id} value={product._id}>
                              {product.name} - ₦{product.price.toLocaleString()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="message">Custom Message (Optional)</Label>
                    <Textarea
                      id="message"
                      placeholder="Add a personal message to your referral link..."
                      value={customMessage}
                      onChange={(e) => setCustomMessage(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleGenerateLink} className="bg-purple-600 hover:bg-purple-700">
                      Generate Link
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                  <p className="text-2xl font-bold text-green-600">
                    ₦{resellerStats?.totalEarnings?.toLocaleString() || '0'}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Sales</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {resellerStats?.totalSales || 0}
                  </p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Links</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {resellerStats?.activeReferralLinks || 0}
                  </p>
                </div>
                <LinkIcon className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {resellerStats?.conversionRate?.toFixed(1) || '0'}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="links" className="space-y-6">
          <TabsList>
            <TabsTrigger value="links">My Links</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="earnings">Earnings</TabsTrigger>
          </TabsList>

          <TabsContent value="links" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Referral Links</CardTitle>
                <CardDescription>
                  Manage your referral links and track their performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {referralLinks && referralLinks.length > 0 ? (
                  <div className="space-y-4">
                    {referralLinks.map((link) => (
                      <div key={link._id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-lg">{link.vendor?.emoji}</span>
                              <h3 className="font-semibold">{link.vendor?.shopName}</h3>
                              {link.product && (
                                <>
                                  <span className="text-gray-400">•</span>
                                  <span className="text-sm text-gray-600">{link.product.name}</span>
                                </>
                              )}
                              <Badge variant={link.isActive ? "default" : "secondary"}>
                                {link.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                              <span className="flex items-center">
                                <Eye className="h-4 w-4 mr-1" />
                                {link.clickCount} clicks
                              </span>
                              <span className="flex items-center">
                                <ShoppingCart className="h-4 w-4 mr-1" />
                                {link.conversionCount} sales
                              </span>
                              <span className="flex items-center">
                                <DollarSign className="h-4 w-4 mr-1" />
                                ₦{link.totalEarnings.toLocaleString()} earned
                              </span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Input
                                value={`https://${link.vendor?.subdomain}.vendorshub.com${link.product ? `/products/${link.productId}` : ''}?ref=${link.linkCode}`}
                                readOnly
                                className="text-xs"
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => copyToClipboard(`https://${link.vendor?.subdomain}.vendorshub.com${link.product ? `/products/${link.productId}` : ''}?ref=${link.linkCode}`)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleToggleStatus(link._id, link.isActive)}
                            >
                              {link.isActive ? (
                                <ToggleRight className="h-4 w-4 text-green-600" />
                              ) : (
                                <ToggleLeft className="h-4 w-4 text-gray-400" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDeleteLink(link._id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No referral links yet</h3>
                    <p className="text-gray-600 mb-4">Create your first referral link to start earning commissions</p>
                    <Button onClick={() => setIsCreateDialogOpen(true)} className="bg-purple-600 hover:bg-purple-700">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Link
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Links</CardTitle>
                <CardDescription>
                  Your best performing referral links by earnings
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topPerformingLinks && topPerformingLinks.length > 0 ? (
                  <div className="space-y-4">
                    {topPerformingLinks.map((link, index) => (
                      <div key={link._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-purple-600">#{index + 1}</span>
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <span>{link.vendor?.emoji}</span>
                              <span className="font-medium">{link.vendor?.shopName}</span>
                              {link.product && (
                                <>
                                  <span className="text-gray-400">•</span>
                                  <span className="text-sm text-gray-600">{link.product.name}</span>
                                </>
                              )}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                              <span>{link.clickCount} clicks</span>
                              <span>{link.conversionCount} conversions</span>
                              <span>{link.clickCount > 0 ? ((link.conversionCount / link.clickCount) * 100).toFixed(1) : 0}% rate</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-600">
                            ₦{link.totalEarnings.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">earned</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No performance data yet</h3>
                    <p className="text-gray-600">Start sharing your referral links to see performance metrics</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="earnings">
            <Card>
              <CardHeader>
                <CardTitle>Recent Earnings</CardTitle>
                <CardDescription>
                  Your recent commission transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {resellerStats?.commissionTransactions && resellerStats.commissionTransactions.length > 0 ? (
                  <div className="space-y-4">
                    {resellerStats.commissionTransactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <div className="font-medium">{transaction.description}</div>
                          <div className="text-sm text-gray-600">
                            {new Date(transaction.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-600">
                            +₦{transaction.amount.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600">Commission</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No earnings yet</h3>
                    <p className="text-gray-600">Start promoting products to earn your first commission</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
