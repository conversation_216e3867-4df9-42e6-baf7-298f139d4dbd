import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Activate user as reseller
export const activateReseller = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Update user to initialize reseller fields
    await ctx.db.patch(args.userId, {
      totalEarnings: user.totalEarnings || 0,
      totalSales: user.totalSales || 0,
      updatedAt: Date.now(),
    });

    return { success: true, message: "Reseller activated successfully" };
  },
});

// Generate referral link for a product or vendor shop
export const generateReferralLink = mutation({
  args: {
    userId: v.id("users"),
    vendorId: v.id("vendors"),
    productId: v.optional(v.id("products")),
    customMessage: v.optional(v.string()),
    commissionRate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Verify user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Verify vendor exists
    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) {
      throw new Error("Vendor not found");
    }

    // If productId provided, verify product exists
    if (args.productId) {
      const product = await ctx.db.get(args.productId);
      if (!product) {
        throw new Error("Product not found");
      }
    }

    // Generate unique link code
    const linkCode = `${user._id.slice(-8)}_${vendor._id.slice(-8)}_${Date.now().toString(36)}`;

    // Default commission rate is 10%
    const commissionRate = args.commissionRate || 10;

    // Create referral link
    const referralLinkId = await ctx.db.insert("referralLinks", {
      userId: args.userId,
      vendorId: args.vendorId,
      productId: args.productId,
      linkCode,
      customMessage: args.customMessage,
      commissionRate,
      isActive: true,
      clickCount: 0,
      conversionCount: 0,
      totalEarnings: 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Generate subdomain URL based on environment
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const domain = process.env.NODE_ENV === 'development'
      ? 'localhost:3000'
      : 'vendorshub.com';

    const baseUrl = `${protocol}://${vendor.subdomain}.${domain}`;

    return {
      referralLinkId,
      linkCode,
      commissionRate,
      url: args.productId
        ? `${baseUrl}/products/${args.productId}?ref=${linkCode}`
        : `${baseUrl}?ref=${linkCode}`,
    };
  },
});

// Get user's referral links
export const getUserReferralLinks = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const links = await ctx.db
      .query("referralLinks")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect();

    // Get vendor and product details for each link
    const linksWithDetails = await Promise.all(
      links.map(async (link) => {
        const vendor = await ctx.db.get(link.vendorId);
        const product = link.productId ? await ctx.db.get(link.productId) : null;

        return {
          ...link,
          vendor: vendor ? {
            shopName: vendor.shopName,
            subdomain: vendor.subdomain,
            emoji: vendor.emoji,
          } : null,
          product: product ? {
            name: product.name,
            price: product.price,
            images: product.images,
          } : null,
        };
      })
    );

    return linksWithDetails;
  },
});

// Track referral link click
export const trackReferralClick = mutation({
  args: {
    linkCode: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const referralLink = await ctx.db
      .query("referralLinks")
      .withIndex("by_code", (q) => q.eq("linkCode", args.linkCode))
      .first();

    if (!referralLink) {
      return { success: false, message: "Referral link not found" };
    }

    // Update click count
    await ctx.db.patch(referralLink._id, {
      clickCount: referralLink.clickCount + 1,
      updatedAt: Date.now(),
    });

    // Create click tracking record
    await ctx.db.insert("referralClicks", {
      referralLinkId: referralLink._id,
      userId: referralLink.userId,
      vendorId: referralLink.vendorId,
      productId: referralLink.productId,
      ipAddress: args.ipAddress,
      userAgent: args.userAgent,
      createdAt: Date.now(),
    });

    return { 
      success: true, 
      referralLink: {
        userId: referralLink.userId,
        vendorId: referralLink.vendorId,
        productId: referralLink.productId,
        commissionRate: referralLink.commissionRate,
      }
    };
  },
});

// Get reseller dashboard stats
export const getResellerStats = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Get referral links count
    const referralLinks = await ctx.db
      .query("referralLinks")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const activeLinks = referralLinks.filter(link => link.isActive);
    const totalClicks = referralLinks.reduce((sum, link) => sum + link.clickCount, 0);
    const totalConversions = referralLinks.reduce((sum, link) => sum + link.conversionCount, 0);

    // Get recent orders with commissions
    const recentOrders = await ctx.db
      .query("orders")
      .withIndex("by_referrer", (q) => q.eq("referrerId", args.userId))
      .order("desc")
      .take(10);

    // Get commission transactions
    const commissionTransactions = await ctx.db
      .query("walletTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("type"), "commission"))
      .order("desc")
      .take(10);

    return {
      totalEarnings: user.totalEarnings || 0,
      totalSales: user.totalSales || 0,
      walletBalance: user.walletBalance || 0,
      totalReferralLinks: referralLinks.length,
      activeReferralLinks: activeLinks.length,
      totalClicks,
      totalConversions,
      conversionRate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0,
      recentOrders,
      commissionTransactions,
    };
  },
});

// Get top performing referral links
export const getTopPerformingLinks = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    
    const links = await ctx.db
      .query("referralLinks")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Sort by earnings and take top performers
    const sortedLinks = links
      .sort((a, b) => b.totalEarnings - a.totalEarnings)
      .slice(0, limit);

    // Get vendor and product details
    const linksWithDetails = await Promise.all(
      sortedLinks.map(async (link) => {
        const vendor = await ctx.db.get(link.vendorId);
        const product = link.productId ? await ctx.db.get(link.productId) : null;

        return {
          ...link,
          vendor: vendor ? {
            shopName: vendor.shopName,
            subdomain: vendor.subdomain,
            emoji: vendor.emoji,
          } : null,
          product: product ? {
            name: product.name,
            price: product.price,
            images: product.images,
          } : null,
        };
      })
    );

    return linksWithDetails;
  },
});

// Toggle referral link status
export const toggleReferralLinkStatus = mutation({
  args: {
    linkId: v.id("referralLinks"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const link = await ctx.db.get(args.linkId);
    if (!link) {
      throw new Error("Referral link not found");
    }

    // Verify ownership
    if (link.userId !== args.userId) {
      throw new Error("Unauthorized");
    }

    await ctx.db.patch(args.linkId, {
      isActive: !link.isActive,
      updatedAt: Date.now(),
    });

    return { success: true, isActive: !link.isActive };
  },
});

// Delete referral link
export const deleteReferralLink = mutation({
  args: {
    linkId: v.id("referralLinks"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const link = await ctx.db.get(args.linkId);
    if (!link) {
      throw new Error("Referral link not found");
    }

    // Verify ownership
    if (link.userId !== args.userId) {
      throw new Error("Unauthorized");
    }

    await ctx.db.delete(args.linkId);
    return { success: true };
  },
});
