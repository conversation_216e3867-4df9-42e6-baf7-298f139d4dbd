import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get user's wallet balance
export const getWalletBalance = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    return user?.walletBalance || 0;
  },
});

// Get wallet transaction history
export const getWalletTransactions = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
    type: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("walletTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    if (args.type) {
      query = query.filter((q) => q.eq(q.field("type"), args.type));
    }

    const transactions = await query.collect();
    const sortedTransactions = transactions.sort((a, b) => b.createdAt - a.createdAt);
    
    return args.limit ? sortedTransactions.slice(0, args.limit) : sortedTransactions;
  },
});

// Credit wallet (add money)
export const creditWallet = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    description: v.string(),
    reference: v.optional(v.string()),
    orderId: v.optional(v.id("orders")),
  },
  handler: async (ctx, args) => {
    if (args.amount <= 0) {
      throw new Error("Amount must be positive");
    }

    // Get current user
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const currentBalance = user.walletBalance || 0;
    const newBalance = currentBalance + args.amount;

    // Update user's wallet balance
    await ctx.db.patch(args.userId, {
      walletBalance: newBalance,
      updatedAt: Date.now(),
    });

    // Create transaction record
    const transactionId = await ctx.db.insert("walletTransactions", {
      userId: args.userId,
      type: "credit",
      amount: args.amount,
      balance: newBalance,
      description: args.description,
      reference: args.reference,
      orderId: args.orderId,
      status: "completed",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      transactionId,
      newBalance,
      amount: args.amount,
    };
  },
});

// Debit wallet (deduct money)
export const debitWallet = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    description: v.string(),
    reference: v.optional(v.string()),
    orderId: v.optional(v.id("orders")),
  },
  handler: async (ctx, args) => {
    if (args.amount <= 0) {
      throw new Error("Amount must be positive");
    }

    // Get current user
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const currentBalance = user.walletBalance || 0;
    
    if (currentBalance < args.amount) {
      throw new Error("Insufficient wallet balance");
    }

    const newBalance = currentBalance - args.amount;

    // Update user's wallet balance
    await ctx.db.patch(args.userId, {
      walletBalance: newBalance,
      updatedAt: Date.now(),
    });

    // Create transaction record
    const transactionId = await ctx.db.insert("walletTransactions", {
      userId: args.userId,
      type: "debit",
      amount: args.amount,
      balance: newBalance,
      description: args.description,
      reference: args.reference,
      orderId: args.orderId,
      status: "completed",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      transactionId,
      newBalance,
      amount: args.amount,
    };
  },
});

// Add commission to wallet
export const addCommission = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    description: v.string(),
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    if (args.amount <= 0) {
      throw new Error("Commission amount must be positive");
    }

    // Get current user
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const currentBalance = user.walletBalance || 0;
    const newBalance = currentBalance + args.amount;

    // Update user's wallet balance and total earnings
    await ctx.db.patch(args.userId, {
      walletBalance: newBalance,
      totalEarnings: (user.totalEarnings || 0) + args.amount,
      updatedAt: Date.now(),
    });

    // Create transaction record
    const transactionId = await ctx.db.insert("walletTransactions", {
      userId: args.userId,
      type: "commission",
      amount: args.amount,
      balance: newBalance,
      description: args.description,
      orderId: args.orderId,
      status: "completed",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      transactionId,
      newBalance,
      amount: args.amount,
    };
  },
});

// Process withdrawal request
export const requestWithdrawal = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    if (args.amount <= 0) {
      throw new Error("Withdrawal amount must be positive");
    }

    // Get current user
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const currentBalance = user.walletBalance || 0;
    
    if (currentBalance < args.amount) {
      throw new Error("Insufficient wallet balance for withdrawal");
    }

    // Create pending withdrawal transaction
    const transactionId = await ctx.db.insert("walletTransactions", {
      userId: args.userId,
      type: "withdrawal",
      amount: args.amount,
      balance: currentBalance, // Balance before withdrawal
      description: args.description || `Withdrawal request of ₦${args.amount}`,
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      transactionId,
      amount: args.amount,
      status: "pending",
    };
  },
});

// Approve withdrawal (admin function)
export const approveWithdrawal = mutation({
  args: {
    transactionId: v.id("walletTransactions"),
    reference: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const transaction = await ctx.db.get(args.transactionId);
    if (!transaction) {
      throw new Error("Transaction not found");
    }

    if (transaction.type !== "withdrawal" || transaction.status !== "pending") {
      throw new Error("Invalid withdrawal transaction");
    }

    // Get user
    const user = await ctx.db.get(transaction.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const currentBalance = user.walletBalance || 0;
    
    if (currentBalance < transaction.amount) {
      throw new Error("Insufficient wallet balance");
    }

    const newBalance = currentBalance - transaction.amount;

    // Update user's wallet balance
    await ctx.db.patch(transaction.userId, {
      walletBalance: newBalance,
      updatedAt: Date.now(),
    });

    // Update transaction status
    await ctx.db.patch(args.transactionId, {
      status: "completed",
      balance: newBalance,
      reference: args.reference,
      updatedAt: Date.now(),
    });

    return {
      transactionId: args.transactionId,
      newBalance,
      amount: transaction.amount,
    };
  },
});

// Get wallet statistics
export const getWalletStats = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const transactions = await ctx.db
      .query("walletTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const totalCredits = transactions
      .filter(t => t.type === "credit" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalDebits = transactions
      .filter(t => t.type === "debit" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalCommissions = transactions
      .filter(t => t.type === "commission" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalWithdrawals = transactions
      .filter(t => t.type === "withdrawal" && t.status === "completed")
      .reduce((sum, t) => sum + t.amount, 0);

    const pendingWithdrawals = transactions
      .filter(t => t.type === "withdrawal" && t.status === "pending")
      .reduce((sum, t) => sum + t.amount, 0);

    // Get recent transactions (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentTransactions = transactions.filter(t => t.createdAt >= thirtyDaysAgo);

    return {
      totalCredits,
      totalDebits,
      totalCommissions,
      totalWithdrawals,
      pendingWithdrawals,
      recentTransactionsCount: recentTransactions.length,
      totalTransactions: transactions.length,
    };
  },
});
