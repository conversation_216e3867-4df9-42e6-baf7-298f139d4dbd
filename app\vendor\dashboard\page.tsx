"use client";

import { useAuth } from "@/lib/auth-context";
import { useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Store, 
  Package, 
  Users, 
  DollarSign, 
  MessageSquare, 
  Settings,
  Plus,
  ExternalLink,
  BarChart3
} from "lucide-react";
import Link from "next/link";

export default function VendorDashboardPage() {
  const { user } = useAuth();
  const router = useRouter();
  
  // Get vendor data
  const vendor = useQuery(api.vendors.getVendorByUserId, 
    user ? { userId: user._id } : "skip"
  );
  
  // Get vendor stats (you'll need to implement these queries)
  // const products = useQuery(api.products.getProductsByVendor, 
  //   vendor ? { vendorId: vendor._id } : "skip"
  // );

  // Redirect if not authenticated
  if (!user) {
    router.push("/auth/login");
    return null;
  }

  // Redirect if not a vendor
  if (user.role !== "vendor") {
    router.push("/dashboard");
    return null;
  }

  // If vendor doesn't exist, redirect to setup
  if (user && !vendor && vendor !== undefined) {
    router.push("/vendor/setup");
    return null;
  }

  // Loading state
  if (vendor === undefined) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: "Total Products",
      value: "0", // Replace with actual count
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Orders",
      value: "0", // Replace with actual count
      icon: BarChart3,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Total Customers",
      value: "0", // Replace with actual count
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Total Earnings",
      value: "₦0", // Replace with actual earnings
      icon: DollarSign,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-2xl">{vendor?.emoji || "🛍️"}</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {vendor?.shopName}
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant={vendor?.isShopActive ? "default" : "secondary"}>
                    {vendor?.isShopActive ? "Active" : "Inactive"}
                  </Badge>
                  <a
                    href={`https://${vendor?.subdomain}.vendorshub.com`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    {vendor?.subdomain}.vendorshub.com
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" asChild>
                <Link href="/vendor/settings">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/vendor/products/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Product Management
              </CardTitle>
              <CardDescription>
                Manage your products, inventory, and pricing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link href="/vendor/products">
                  <Package className="h-4 w-4 mr-2" />
                  View All Products
                </Link>
              </Button>
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link href="/vendor/products/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Product
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Orders & Sales
              </CardTitle>
              <CardDescription>
                Track your orders and sales performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link href="/vendor/orders">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Orders
                </Link>
              </Button>
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link href="/vendor/analytics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Sales Analytics
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest shop activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <Store className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No recent activity yet.</p>
              <p className="text-sm mt-1">
                Start by adding your first product to see activity here.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
