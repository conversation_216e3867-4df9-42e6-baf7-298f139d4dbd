"use client";

import { useState } from "react";
import { useCart } from "@/lib/cart-context";
import { useAuth } from "@/lib/auth-context";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import {
  ShoppingCart as ShoppingCartIcon,
  Plus,
  Minus,
  Trash2,
  MessageCircle,
  CreditCard,
  Wallet
} from "lucide-react";

interface ShoppingCartProps {
  trigger?: React.ReactNode;
}

export function ShoppingCart({ trigger }: ShoppingCartProps) {
  const {
    state,
    removeItem,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotalPrice,
    getItemsByVendor,
    closeCart
  } = useCart();

  const { user } = useAuth();
  const [checkoutMethod, setCheckoutMethod] = useState<"whatsapp" | "wallet" | "paystack">("whatsapp");
  const [isProcessing, setIsProcessing] = useState(false);

  // Queries
  const walletBalance = useQuery(api.wallet.getWalletBalance,
    user?._id ? { userId: user._id } : "skip"
  );

  // Mutations
  const createOrder = useMutation(api.orders.createOrder);
  const debitWallet = useMutation(api.wallet.debitWallet);
  const initializePayment = useAction(api.paystack.initializePayment);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
    }).format(price);
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(productId as any);
    } else {
      updateQuantity(productId as any, newQuantity);
    }
  };

  const handleWhatsAppCheckout = () => {
    const itemsByVendor = getItemsByVendor();
    
    // For now, we'll handle single vendor checkout
    // In a real app, you might want to split orders by vendor
    const vendorIds = Object.keys(itemsByVendor);
    if (vendorIds.length === 0) return;

    const firstVendorItems = itemsByVendor[vendorIds[0]];
    const firstItem = firstVendorItems[0];
    
    // Create WhatsApp message
    let message = "Hello! I'd like to place an order:\n\n";
    
    Object.entries(itemsByVendor).forEach(([vendorId, items]) => {
      const vendorName = items[0].vendorName || "Vendor";
      message += `*From ${vendorName}:*\n`;
      
      items.forEach(item => {
        message += `• ${item.productName} x${item.quantity} - ${formatPrice(item.price * item.quantity)}\n`;
      });
      message += "\n";
    });

    message += `*Total: ${formatPrice(getTotalPrice())}*\n\n`;
    message += "Please confirm availability and provide payment details.";

    // For demo purposes, we'll use a placeholder number
    // In a real app, you'd get the vendor's WhatsApp number from the database
    const whatsappNumber = "2348000000000"; // Replace with actual vendor WhatsApp
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    
    window.open(whatsappUrl, '_blank');
    closeCart();
  };

  const handleWalletCheckout = async () => {
    if (!user) {
      alert("Please log in to use wallet payment");
      return;
    }

    const totalAmount = getTotalPrice();
    const currentBalance = walletBalance ?? 0;

    if (currentBalance < totalAmount) {
      alert(`Insufficient wallet balance. You have ${formatPrice(currentBalance)} but need ${formatPrice(totalAmount)}`);
      return;
    }

    setIsProcessing(true);
    try {
      const itemsByVendor = getItemsByVendor();

      // Process orders for each vendor
      for (const [vendorId, items] of Object.entries(itemsByVendor)) {
        const vendorTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Create order
        const orderResult = await createOrder({
          buyerId: user._id,
          vendorId: vendorId as any,
          items: items.map(item => ({
            productId: item.productId as any,
            productName: item.productName,
            price: item.price,
            quantity: item.quantity,
            totalPrice: item.price * item.quantity,
          })),
          subtotal: vendorTotal,
          total: vendorTotal,
          paymentMethod: "wallet",
          shippingAddress: {
            name: user.name || "",
            phone: user.phone,
            address: "", // You might want to collect this
            city: "",
            state: "",
            country: "Nigeria",
          },
        });

        // Debit wallet
        await debitWallet({
          userId: user._id,
          amount: vendorTotal,
          description: `Payment for order #${orderResult.orderNumber}`,
          orderId: orderResult.orderId,
        });
      }

      clearCart();
      closeCart();
      alert("Order placed successfully! Payment deducted from wallet.");
    } catch (error) {
      console.error("Wallet checkout failed:", error);
      alert("Failed to process wallet payment. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaystackCheckout = async () => {
    if (!user) {
      alert("Please log in to use card payment");
      return;
    }

    setIsProcessing(true);
    try {
      const totalAmount = getTotalPrice() * 100; // Convert to kobo

      const result = await initializePayment({
        userId: user._id,
        amount: totalAmount,
        email: `${user.phone}@vendorshub.com`,
        purpose: "order_payment",
      });

      if (result.status) {
        // Redirect to Paystack checkout
        window.open(result.data.authorization_url, '_blank');
        closeCart();
      }
    } catch (error) {
      console.error("Paystack checkout failed:", error);
      alert("Failed to initialize payment. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCheckout = () => {
    switch (checkoutMethod) {
      case "whatsapp":
        handleWhatsAppCheckout();
        break;
      case "wallet":
        handleWalletCheckout();
        break;
      case "paystack":
        handlePaystackCheckout();
        break;
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="relative">
      <ShoppingCartIcon className="h-4 w-4" />
      {getTotalItems() > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
        >
          {getTotalItems()}
        </Badge>
      )}
    </Button>
  );

  return (
    <Sheet open={state.isOpen} onOpenChange={(open) => !open && closeCart()}>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <ShoppingCartIcon className="h-5 w-5" />
            Shopping Cart ({getTotalItems()} items)
          </SheetTitle>
          <SheetDescription>
            Review your items and proceed to checkout
          </SheetDescription>
        </SheetHeader>

        <div className="flex flex-col h-full">
          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto py-4">
            {state.items.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <ShoppingCartIcon className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
                <p className="text-gray-500">Add some products to get started!</p>
              </div>
            ) : (
              <div className="space-y-4">
                {Object.entries(getItemsByVendor()).map(([vendorId, items]) => (
                  <Card key={vendorId}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium">
                        {items[0].vendorName || "Vendor Store"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {items.map((item) => (
                        <div key={item.productId} className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <img
                              src={item.image || "/placeholder-product.jpg"}
                              alt={item.productName}
                              className="h-12 w-12 rounded object-cover"
                            />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {item.productName}
                            </h4>
                            <p className="text-sm text-gray-500">
                              {formatPrice(item.price)} each
                            </p>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleQuantityChange(item.productId, item.quantity - 1)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => handleQuantityChange(item.productId, parseInt(e.target.value) || 1)}
                              className="h-8 w-16 text-center"
                            />
                            
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleQuantityChange(item.productId, item.quantity + 1)}
                              disabled={!item.isUnlimitedStock && item.quantity >= (item.maxStock || 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              onClick={() => removeItem(item.productId)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Cart Summary & Checkout */}
          {state.items.length > 0 && (
            <div className="border-t pt-4 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-base font-medium">
                  <span>Total</span>
                  <span>{formatPrice(getTotalPrice())}</span>
                </div>
                <p className="text-sm text-gray-500">
                  Shipping and taxes calculated at checkout
                </p>
              </div>

              {/* Checkout Method Selection */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Choose checkout method:</h4>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant={checkoutMethod === "whatsapp" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCheckoutMethod("whatsapp")}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <MessageCircle className="h-4 w-4 mb-1" />
                    <span className="text-xs">WhatsApp</span>
                  </Button>
                  
                  <Button
                    variant={checkoutMethod === "wallet" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCheckoutMethod("wallet")}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <Wallet className="h-4 w-4 mb-1" />
                    <span className="text-xs">Wallet</span>
                  </Button>
                  
                  <Button
                    variant={checkoutMethod === "paystack" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCheckoutMethod("paystack")}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <CreditCard className="h-4 w-4 mb-1" />
                    <span className="text-xs">Card</span>
                  </Button>
                </div>
              </div>

              {/* Wallet Balance Display */}
              {checkoutMethod === "wallet" && user && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Wallet Balance:</span>
                    <span className="font-medium">{formatPrice(walletBalance ?? 0)}</span>
                  </div>
                  {(walletBalance ?? 0) < getTotalPrice() && (
                    <p className="text-xs text-red-600 mt-1">
                      Insufficient balance. Need {formatPrice(getTotalPrice() - (walletBalance ?? 0))} more.
                    </p>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Button
                  onClick={handleCheckout}
                  className="w-full"
                  disabled={isProcessing || Boolean(checkoutMethod === "wallet" && user && (walletBalance ?? 0) < getTotalPrice())}
                >
                  {isProcessing ? "Processing..." : (
                    <>
                      {checkoutMethod === "whatsapp" && "Continue on WhatsApp"}
                      {checkoutMethod === "wallet" && "Pay with Wallet"}
                      {checkoutMethod === "paystack" && "Pay with Card"}
                    </>
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={clearCart}
                  className="w-full"
                >
                  Clear Cart
                </Button>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
