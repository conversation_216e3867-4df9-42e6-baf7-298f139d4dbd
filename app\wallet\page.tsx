"use client";

import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  Wallet, 
  Plus, 
  Minus, 
  ArrowUpRight, 
  ArrowDownLeft,
  CreditCard,
  History,
  TrendingUp,
  DollarSign
} from "lucide-react";

export default function WalletPage() {
  const { user } = useAuth();
  const [fundAmount, setFundAmount] = useState("");
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Queries
  const walletBalance = useQuery(api.wallet.getWalletBalance, 
    user?._id ? { userId: user._id } : "skip"
  );
  
  const walletStats = useQuery(api.wallet.getWalletStats,
    user?._id ? { userId: user._id } : "skip"
  );
  
  const transactions = useQuery(api.wallet.getWalletTransactions,
    user?._id ? { userId: user._id, limit: 10 } : "skip"
  );

  const paymentStats = useQuery(api.paystack.getPaymentStats,
    user?._id ? { userId: user._id } : "skip"
  );

  // Mutations and Actions
  const initializePayment = useAction(api.paystack.initializePayment);
  const requestWithdrawal = useMutation(api.wallet.requestWithdrawal);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
    }).format(price);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "credit":
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
      case "debit":
        return <ArrowUpRight className="h-4 w-4 text-red-600" />;
      case "commission":
        return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case "withdrawal":
        return <Minus className="h-4 w-4 text-orange-600" />;
      case "refund":
        return <ArrowDownLeft className="h-4 w-4 text-purple-600" />;
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case "credit":
        return "text-green-600";
      case "debit":
        return "text-red-600";
      case "commission":
        return "text-blue-600";
      case "withdrawal":
        return "text-orange-600";
      case "refund":
        return "text-purple-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleFundWallet = async () => {
    if (!user || !fundAmount || parseFloat(fundAmount) <= 0) return;

    setIsLoading(true);
    try {
      const amount = parseFloat(fundAmount) * 100; // Convert to kobo
      const result = await initializePayment({
        userId: user._id,
        amount,
        email: `${user.phone}@vendorshub.com`,
        purpose: "wallet_funding",
      });

      if (result.status) {
        // Redirect to Paystack checkout
        window.open(result.data.authorization_url, '_blank');
        setFundAmount("");
      }
    } catch (error) {
      console.error("Failed to initialize payment:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWithdrawal = async () => {
    if (!user || !withdrawAmount || parseFloat(withdrawAmount) <= 0) return;

    setIsLoading(true);
    try {
      const amount = parseFloat(withdrawAmount);
      await requestWithdrawal({
        userId: user._id,
        amount,
        description: `Withdrawal request of ${formatPrice(amount)}`,
      });
      
      setWithdrawAmount("");
      alert("Withdrawal request submitted successfully. It will be processed within 24 hours.");
    } catch (error) {
      console.error("Failed to request withdrawal:", error);
      alert("Failed to submit withdrawal request. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return <div>Please log in to access your wallet.</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Wallet</h1>
        <p className="text-gray-600">Manage your funds and view transaction history</p>
      </div>

      {/* Wallet Balance Card */}
      <Card className="mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardContent className="p-8">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 mb-2">Available Balance</p>
              <p className="text-4xl font-bold">
                {formatPrice(walletBalance || 0)}
              </p>
            </div>
            <Wallet className="h-16 w-16 text-blue-200" />
          </div>
          
          <div className="flex space-x-4 mt-6">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="secondary" className="flex-1">
                  <Plus className="h-4 w-4 mr-2" />
                  Fund Wallet
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Fund Your Wallet</DialogTitle>
                  <DialogDescription>
                    Add money to your wallet using Paystack payment gateway
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">Amount (₦)</Label>
                    <Input
                      id="amount"
                      type="number"
                      min="100"
                      step="0.01"
                      value={fundAmount}
                      onChange={(e) => setFundAmount(e.target.value)}
                      placeholder="Enter amount"
                    />
                  </div>
                  <Button 
                    onClick={handleFundWallet} 
                    disabled={isLoading || !fundAmount || parseFloat(fundAmount) < 100}
                    className="w-full"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    {isLoading ? "Processing..." : "Pay with Paystack"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex-1 text-white border-white hover:bg-white hover:text-blue-600">
                  <Minus className="h-4 w-4 mr-2" />
                  Withdraw
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Withdraw Funds</DialogTitle>
                  <DialogDescription>
                    Request a withdrawal from your wallet balance
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="withdrawAmount">Amount (₦)</Label>
                    <Input
                      id="withdrawAmount"
                      type="number"
                      min="100"
                      max={walletBalance || 0}
                      step="0.01"
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      placeholder="Enter amount"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Available: {formatPrice(walletBalance || 0)}
                    </p>
                  </div>
                  <Button 
                    onClick={handleWithdrawal} 
                    disabled={isLoading || !withdrawAmount || parseFloat(withdrawAmount) > (walletBalance || 0)}
                    className="w-full"
                  >
                    {isLoading ? "Processing..." : "Request Withdrawal"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="transactions" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <History className="h-5 w-5 mr-2" />
                Recent Transactions
              </CardTitle>
              <CardDescription>Your latest wallet transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {!transactions || transactions.length === 0 ? (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
                  <p className="text-gray-500">Your transaction history will appear here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction: any) => (
                    <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <p className="font-medium text-gray-900">{transaction.description}</p>
                          <p className="text-sm text-gray-500">{formatDate(transaction.createdAt)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                          {transaction.type === "debit" || transaction.type === "withdrawal" ? "-" : "+"}
                          {formatPrice(transaction.amount)}
                        </p>
                        <Badge className={getStatusColor(transaction.status)}>
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          {walletStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <ArrowDownLeft className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Credits</p>
                      <p className="text-2xl font-bold text-gray-900">{formatPrice(walletStats.totalCredits)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <ArrowUpRight className="h-8 w-8 text-red-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Debits</p>
                      <p className="text-2xl font-bold text-gray-900">{formatPrice(walletStats.totalDebits)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <TrendingUp className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Commissions</p>
                      <p className="text-2xl font-bold text-gray-900">{formatPrice(walletStats.totalCommissions)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Minus className="h-8 w-8 text-orange-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Withdrawals</p>
                      <p className="text-2xl font-bold text-gray-900">{formatPrice(walletStats.totalWithdrawals)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="payments">
          {paymentStats && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <CreditCard className="h-8 w-8 text-blue-600" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Payments</p>
                        <p className="text-2xl font-bold text-gray-900">{paymentStats.totalPayments}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <TrendingUp className="h-8 w-8 text-green-600" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Success Rate</p>
                        <p className="text-2xl font-bold text-gray-900">{paymentStats.successRate.toFixed(1)}%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <DollarSign className="h-8 w-8 text-purple-600" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Amount</p>
                        <p className="text-2xl font-bold text-gray-900">{formatPrice(paymentStats.totalAmount)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <Wallet className="h-8 w-8 text-indigo-600" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Wallet Funding</p>
                        <p className="text-2xl font-bold text-gray-900">{formatPrice(paymentStats.walletFunding)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
