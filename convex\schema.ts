import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table - handles all user types (User, Vendor, Admin)
  users: defineTable({
    // Basic user info
    name: v.string(),
    phone: v.string(), // Primary identifier for OTP login
    address: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
    
    // User role and status
    role: v.union(
      v.literal("user"),
      v.literal("vendor"),
      v.literal("admin")
    ),
    isActive: v.boolean(),
    isPro: v.boolean(), // For Pro features like course access

    // Reselling capabilities for normal users
    totalEarnings: v.optional(v.number()), // Total earnings from reselling
    totalSales: v.optional(v.number()), // Total number of sales made

    // Wallet
    walletBalance: v.optional(v.number()), // Current wallet balance

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
    
    // Verification
    isPhoneVerified: v.boolean(),
    
    // Profile image
    profileImage: v.optional(v.string()), // Cloudinary URL
  })
  .index("by_phone", ["phone"])
  .index("by_role", ["role"])
  .index("by_active", ["isActive"]),

  // Vendors table - extends user info for vendors
  vendors: defineTable({
    userId: v.id("users"), // Reference to users table
    subdomain: v.string(), // Unique subdomain
    shopName: v.string(),
    shopDescription: v.optional(v.string()),
    shopLogo: v.optional(v.string()), // Cloudinary URL
    shopBanner: v.optional(v.string()), // Cloudinary URL
    emoji: v.string(), // Shop emoji branding
    
    // Shop settings
    isShopActive: v.boolean(),
    defaultWhatsappNumber: v.optional(v.string()),
    
    // Business info
    businessAddress: v.optional(v.string()),
    businessPhone: v.optional(v.string()),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_subdomain", ["subdomain"])
  .index("by_active", ["isShopActive"]),

  // Products table
  products: defineTable({
    vendorId: v.id("vendors"),
    
    // Product details
    name: v.string(),
    description: v.string(),
    price: v.number(),
    compareAtPrice: v.optional(v.number()), // Original price for discounts
    
    // Product media
    images: v.array(v.string()), // Array of Cloudinary URLs
    videos: v.optional(v.array(v.string())), // Array of Cloudinary URLs
    
    // Product organization
    category: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    
    // Inventory
    stockQuantity: v.optional(v.number()),
    isUnlimitedStock: v.boolean(),
    
    // Product settings
    isActive: v.boolean(),
    isDigital: v.boolean(), // For digital products/courses
    
    // WhatsApp integration
    whatsappNumber: v.optional(v.string()), // Override vendor default
    whatsappMessage: v.optional(v.string()), // Custom message template
    
    // SEO
    slug: v.optional(v.string()),
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_vendor", ["vendorId"])
  .index("by_active", ["isActive"])
  .index("by_category", ["category"])
  .index("by_slug", ["slug"]),

  // Courses table (Pro feature)
  courses: defineTable({
    vendorId: v.id("vendors"),
    productId: v.optional(v.id("products")), // Link to product if course is sold as product
    
    // Course details
    title: v.string(),
    description: v.string(),
    price: v.number(),
    
    // Course content
    videoUrl: v.string(), // Cloudinary video URL
    duration: v.optional(v.number()), // Duration in minutes
    thumbnail: v.optional(v.string()), // Cloudinary image URL
    
    // Course settings
    isActive: v.boolean(),
    isPublic: v.boolean(), // Public courses visible to all
    
    // Access control
    requiresPro: v.boolean(), // Requires Pro subscription to access
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_vendor", ["vendorId"])
  .index("by_active", ["isActive"])
  .index("by_public", ["isPublic"]),

  // Orders table
  orders: defineTable({
    // Order parties
    buyerId: v.optional(v.id("users")), // Optional for guest orders
    vendorId: v.id("vendors"),
    referrerId: v.optional(v.id("users")), // If order came through user referral/resale

    // Order details
    orderNumber: v.string(), // Unique order identifier
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("processing"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("refunded")
    ),

    // Order items
    items: v.array(v.object({
      productId: v.id("products"),
      productName: v.string(),
      quantity: v.number(),
      price: v.number(),
      totalPrice: v.number(),
    })),

    // Order totals
    subtotal: v.number(),
    tax: v.optional(v.number()),
    shipping: v.optional(v.number()),
    discount: v.optional(v.number()),
    total: v.number(),

    // Payment info
    paymentMethod: v.union(
      v.literal("whatsapp"),
      v.literal("wallet"),
      v.literal("paystack")
    ),
    paymentStatus: v.union(
      v.literal("pending"),
      v.literal("paid"),
      v.literal("failed"),
      v.literal("refunded")
    ),
    paymentReference: v.optional(v.string()), // Payment reference from Paystack or other providers

    // Shipping info
    shippingAddress: v.optional(v.object({
      name: v.string(),
      phone: v.string(),
      address: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
    })),

    // Commission tracking
    commissionRate: v.optional(v.number()), // Commission percentage for referrer
    commissionAmount: v.optional(v.number()),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_buyer", ["buyerId"])
  .index("by_vendor", ["vendorId"])
  .index("by_referrer", ["referrerId"])
  .index("by_status", ["status"])
  .index("by_order_number", ["orderNumber"]),

  // Product requests table
  productRequests: defineTable({
    requesterId: v.optional(v.id("users")), // User making the request
    vendorId: v.id("vendors"), // Vendor receiving the request

    // Request details
    productName: v.string(),
    description: v.string(),
    estimatedPrice: v.optional(v.number()),

    // Contact info (for guest requests)
    guestName: v.optional(v.string()),
    guestPhone: v.optional(v.string()),
    guestEmail: v.optional(v.string()),

    // Request status
    status: v.union(
      v.literal("pending"),
      v.literal("acknowledged"),
      v.literal("quoted"),
      v.literal("accepted"),
      v.literal("rejected"),
      v.literal("completed")
    ),

    // Vendor response
    vendorResponse: v.optional(v.string()),
    quotedPrice: v.optional(v.number()),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_requester", ["requesterId"])
  .index("by_vendor", ["vendorId"])
  .index("by_status", ["status"]),

  // Wallet transactions table
  walletTransactions: defineTable({
    userId: v.id("users"),

    // Transaction details
    type: v.union(
      v.literal("credit"), // Money added to wallet
      v.literal("debit"),  // Money deducted from wallet
      v.literal("commission"), // Commission earned
      v.literal("withdrawal"), // Money withdrawn
      v.literal("refund") // Refund received
    ),
    amount: v.number(),
    balance: v.number(), // Balance after transaction

    // Transaction metadata
    description: v.string(),
    reference: v.optional(v.string()), // External reference (Paystack, etc.)
    orderId: v.optional(v.id("orders")), // Related order if applicable

    // Status
    status: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    ),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_type", ["type"])
  .index("by_status", ["status"])
  .index("by_created", ["createdAt"]),

  // Paystack payments table
  paystackPayments: defineTable({
    userId: v.id("users"),

    // Payment details
    amount: v.number(), // Amount in kobo
    email: v.string(),
    reference: v.string(), // Paystack reference

    // Related entities
    orderId: v.optional(v.id("orders")),

    // Payment metadata
    purpose: v.string(), // "wallet_funding", "order_payment", etc.
    status: v.string(), // "pending", "success", "failed", "abandoned"
    verifiedAmount: v.optional(v.number()), // Verified amount from Paystack

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_reference", ["reference"])
  .index("by_status", ["status"])
  .index("by_purpose", ["purpose"]),

  // Referral links table - for normal users to resell products
  referralLinks: defineTable({
    userId: v.id("users"), // User who created the referral link
    vendorId: v.id("vendors"), // Vendor whose products are being resold
    productId: v.optional(v.id("products")), // Specific product (optional, can be for entire shop)

    // Link details
    linkCode: v.string(), // Unique code for the referral link
    customMessage: v.optional(v.string()), // Custom message from user

    // Commission settings
    commissionRate: v.number(), // Commission percentage

    // Link settings
    isActive: v.boolean(),
    expiresAt: v.optional(v.number()), // Optional expiration date

    // Performance tracking
    clickCount: v.number(), // Number of times link was clicked
    conversionCount: v.number(), // Number of successful conversions
    totalEarnings: v.number(), // Total earnings from this link

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_vendor", ["vendorId"])
  .index("by_product", ["productId"])
  .index("by_code", ["linkCode"])
  .index("by_active", ["isActive"]),

  // Referral clicks tracking table
  referralClicks: defineTable({
    referralLinkId: v.id("referralLinks"),
    userId: v.id("users"), // Reseller who owns the link
    vendorId: v.id("vendors"),
    productId: v.optional(v.id("products")),

    // Click tracking data
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),

    // Timestamps
    createdAt: v.number(),
  })
  .index("by_link", ["referralLinkId"])
  .index("by_user", ["userId"])
  .index("by_vendor", ["vendorId"]),

  // Chat conversations table
  conversations: defineTable({
    // Participants
    participants: v.array(v.id("users")), // Array of user IDs in conversation

    // Conversation metadata
    type: v.union(
      v.literal("direct"), // Direct message between two users
      v.literal("support") // Support conversation with vendor
    ),
    title: v.optional(v.string()), // Optional conversation title

    // Last message info for quick display
    lastMessage: v.optional(v.string()),
    lastMessageAt: v.optional(v.number()),
    lastMessageBy: v.optional(v.id("users")),

    // Status
    isActive: v.boolean(),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_participants", ["participants"])
  .index("by_last_message", ["lastMessageAt"])
  .index("by_active", ["isActive"]),

  // Chat messages table
  messages: defineTable({
    conversationId: v.id("conversations"),
    senderId: v.id("users"),

    // Message content
    content: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("image"),
      v.literal("file"),
      v.literal("product_share") // When sharing a product
    ),

    // Media attachments
    attachments: v.optional(v.array(v.object({
      url: v.string(), // Cloudinary URL
      type: v.string(), // file type
      name: v.string(), // original filename
      size: v.optional(v.number()), // file size in bytes
    }))),

    // Product sharing
    sharedProductId: v.optional(v.id("products")),

    // Message status
    isRead: v.boolean(),
    readBy: v.optional(v.array(v.object({
      userId: v.id("users"),
      readAt: v.number(),
    }))),

    // Moderation
    isFlagged: v.boolean(),
    flagReason: v.optional(v.string()),

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_conversation", ["conversationId"])
  .index("by_sender", ["senderId"])
  .index("by_created", ["createdAt"])
  .index("by_flagged", ["isFlagged"]),

  // Platform settings table
  platformSettings: defineTable({
    key: v.string(), // Setting key (e.g., "commission_rate", "maintenance_mode")
    value: v.union(v.string(), v.number(), v.boolean()), // Setting value
    description: v.optional(v.string()), // Description of the setting

    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
  .index("by_key", ["key"]),

  // Notifications table
  notifications: defineTable({
    userId: v.id("users"), // User receiving the notification

    // Notification content
    title: v.string(),
    message: v.string(),
    type: v.union(
      v.literal("order"),
      v.literal("payment"),
      v.literal("chat"),
      v.literal("product_request"),
      v.literal("commission"),
      v.literal("system")
    ),

    // Related entities
    orderId: v.optional(v.id("orders")),
    conversationId: v.optional(v.id("conversations")),
    productRequestId: v.optional(v.id("productRequests")),

    // Status
    isRead: v.boolean(),
    readAt: v.optional(v.number()),

    // Timestamps
    createdAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_type", ["type"])
  .index("by_read", ["isRead"])
  .index("by_created", ["createdAt"]),
});
