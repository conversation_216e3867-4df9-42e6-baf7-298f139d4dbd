"use client";

import { useEffect } from 'react';
import { useReferralTracking } from '@/hooks/useReferralTracking';

export function ReferralTracker() {
  const { isTracking } = useReferralTracking();

  // This component handles referral tracking automatically
  // No UI needed, just the tracking logic
  return null;
}

// Component to show referral info (optional)
export function ReferralInfo() {
  const { referralData } = useReferralTracking();

  if (!referralData) return null;

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-green-800">
            <span className="font-medium">Referral Applied!</span> You're supporting a reseller with this purchase.
          </p>
        </div>
      </div>
    </div>
  );
}
