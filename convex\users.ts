import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new user
export const createUser = mutation({
  args: {
    name: v.string(),
    phone: v.string(),
    role: v.union(v.literal("user"), v.literal("reseller"), v.literal("vendor"), v.literal("admin")),
    address: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user with this phone already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();

    if (existingUser) {
      throw new Error("User with this phone number already exists");
    }

    const now = Date.now();
    
    const userId = await ctx.db.insert("users", {
      name: args.name,
      phone: args.phone,
      role: args.role,
      address: args.address,
      whatsappNumber: args.whatsappNumber,
      isActive: true,
      isPro: false,
      isPhoneVerified: false,
      createdAt: now,
      updatedAt: now,
    });

    return userId;
  },
});

// Get user by phone number
export const getUserByPhone = query({
  args: { phone: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();
  },
});

// Get user by ID
export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  },
});

// Update user profile
export const updateUser = mutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    address: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
    profileImage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    
    await ctx.db.patch(userId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(userId);
  },
});

// Verify user phone
export const verifyPhone = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isPhoneVerified: true,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.userId);
  },
});

// Upgrade user to Pro
export const upgradeUserToPro = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isPro: true,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.userId);
  },
});

// Get users by role
export const getUsersByRole = query({
  args: {
    role: v.union(v.literal("user"), v.literal("reseller"), v.literal("vendor"), v.literal("admin")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("users")
      .withIndex("by_role", (q) => q.eq("role", args.role));

    if (args.limit) {
      return await query.take(args.limit);
    }

    return await query.collect();
  },
});

// Get active users
export const getActiveUsers = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("users")
      .withIndex("by_active", (q) => q.eq("isActive", true));

    if (args.limit) {
      return await query.take(args.limit);
    }

    return await query.collect();
  },
});

// Deactivate user
export const deactivateUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.userId);
  },
});

// Activate user
export const activateUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isActive: true,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.userId);
  },
});

// Change user role
export const changeUserRole = mutation({
  args: {
    userId: v.id("users"),
    newRole: v.union(v.literal("user"), v.literal("reseller"), v.literal("vendor"), v.literal("admin")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      role: args.newRole,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.userId);
  },
});
