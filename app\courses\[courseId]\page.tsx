"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/lib/auth-context";
import { CoursePlayer } from "@/components/courses/course-player";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Loader2 } from "lucide-react";
import { Id } from "@/convex/_generated/dataModel";
import Link from "next/link";
import { useParams } from "next/navigation";

export default function CourseViewPage() {
  const params = useParams();
  const { user } = useAuth();
  const courseId = params.courseId as Id<"courses">;

  // Get course details with access control
  const courseData = useQuery(
    api.courses.getCourseById,
    courseId ? { courseId, userId: user?._id } : "skip"
  );

  if (!courseId) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Invalid Course</h1>
          <p className="text-gray-600 mt-2">The course you're looking for doesn't exist.</p>
          <Link href="/courses">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  if (courseData === undefined) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading course...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!courseData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Course Not Found</h1>
          <p className="text-gray-600 mt-2">
            The course you're looking for doesn't exist or is no longer available.
          </p>
          <Link href="/courses">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/courses">
              <Button variant="ghost" className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Courses
              </Button>
            </Link>
            
            <div className="text-sm text-gray-600">
              Course by {courseData.vendor?.shopName || "Unknown Vendor"}
            </div>
          </div>
        </div>
      </div>

      {/* Course Player */}
      <div className="container mx-auto px-4 py-8">
        <CoursePlayer
          course={{
            ...courseData,
            vendor: courseData.vendor ? {
              shopName: courseData.vendor.shopName,
              shopLogo: courseData.vendor.shopLogo,
            } : undefined,
          }}
          hasAccess={courseData.hasAccess}
        />
      </div>
    </div>
  );
}
