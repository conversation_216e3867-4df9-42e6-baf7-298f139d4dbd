import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Register a new user with phone number (without OTP for now)
export const registerUser = mutation({
  args: {
    name: v.string(),
    phone: v.string(),
    role: v.optional(v.union(v.literal("user"), v.literal("vendor"))),
    address: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user with this phone already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();

    if (existingUser) {
      throw new Error("User with this phone number already exists");
    }

    const now = Date.now();
    
    const userId = await ctx.db.insert("users", {
      name: args.name,
      phone: args.phone,
      role: args.role || "user",
      address: args.address,
      whatsappNumber: args.whatsappNumber,
      isActive: true,
      isPro: false,
      isPhoneVerified: true, // Set to true for now since we're not using OTP
      createdAt: now,
      updatedAt: now,
    });

    // Get the created user
    const user = await ctx.db.get(userId);
    
    return {
      success: true,
      user,
      message: "User registered successfully"
    };
  },
});

// Login user with phone number (simple login without password for now)
export const loginUser = mutation({
  args: {
    phone: v.string(),
  },
  handler: async (ctx, args) => {
    // Find user by phone number
    const user = await ctx.db
      .query("users")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();

    if (!user) {
      throw new Error("User not found. Please register first.");
    }

    if (!user.isActive) {
      throw new Error("User account is deactivated. Please contact support.");
    }

    // Update last login time
    await ctx.db.patch(user._id, {
      updatedAt: Date.now(),
    });

    return {
      success: true,
      user,
      message: "Login successful"
    };
  },
});

// Get current user session (for client-side auth state)
export const getCurrentUser = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    if (!args.userId) {
      return null;
    }

    const user = await ctx.db.get(args.userId);
    
    if (!user || !user.isActive) {
      return null;
    }

    return user;
  },
});

// Update user profile
export const updateProfile = mutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    address: v.optional(v.string()),
    whatsappNumber: v.optional(v.string()),
    profileImage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    
    // Verify user exists
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    await ctx.db.patch(userId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(userId);
  },
});

// Change user role (admin only)
export const changeUserRole = mutation({
  args: {
    adminUserId: v.id("users"),
    targetUserId: v.id("users"),
    newRole: v.union(v.literal("user"), v.literal("vendor"), v.literal("admin")),
  },
  handler: async (ctx, args) => {
    // Verify admin user
    const adminUser = await ctx.db.get(args.adminUserId);
    if (!adminUser || adminUser.role !== "admin") {
      throw new Error("Only admin users can change user roles");
    }

    // Verify target user exists
    const targetUser = await ctx.db.get(args.targetUserId);
    if (!targetUser) {
      throw new Error("Target user not found");
    }

    await ctx.db.patch(args.targetUserId, {
      role: args.newRole,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.targetUserId);
  },
});

// Deactivate user account
export const deactivateAccount = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return { success: true, message: "Account deactivated successfully" };
  },
});

// Reactivate user account (admin only)
export const reactivateAccount = mutation({
  args: {
    adminUserId: v.id("users"),
    targetUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Verify admin user
    const adminUser = await ctx.db.get(args.adminUserId);
    if (!adminUser || adminUser.role !== "admin") {
      throw new Error("Only admin users can reactivate accounts");
    }

    await ctx.db.patch(args.targetUserId, {
      isActive: true,
      updatedAt: Date.now(),
    });

    return { success: true, message: "Account reactivated successfully" };
  },
});

// Check if phone number is available
export const isPhoneAvailable = query({
  args: { phone: v.string() },
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_phone", (q) => q.eq("phone", args.phone))
      .first();

    return !existingUser;
  },
});

// Get user statistics (for admin dashboard)
export const getUserStats = query({
  args: {},
  handler: async (ctx, args) => {
    const allUsers = await ctx.db.query("users").collect();
    
    const stats = {
      total: allUsers.length,
      active: allUsers.filter(u => u.isActive).length,
      inactive: allUsers.filter(u => !u.isActive).length,
      byRole: {
        user: allUsers.filter(u => u.role === "user").length,
        vendor: allUsers.filter(u => u.role === "vendor").length,
        admin: allUsers.filter(u => u.role === "admin").length,
      },
      pro: allUsers.filter(u => u.isPro).length,
      verified: allUsers.filter(u => u.isPhoneVerified).length,
    };

    return stats;
  },
});
