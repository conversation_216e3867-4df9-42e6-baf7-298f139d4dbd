"use client";

import Link from "next/link";
import { useAuth } from "@/lib/auth-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart } from "@/components/cart/shopping-cart";
import {
  Store,
  User,
  Wallet,
  ShoppingBag,
  LogOut,
  Settings,
  Package,
  Users,
  Video
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function Navigation() {
  const { user, logout } = useAuth();

  return (
    <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Store className="h-5 w-5 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              VendorsHub
            </span>
          </Link>

          {/* Navigation Items */}
          <div className="flex items-center space-x-4">
            {/* Courses Link */}
            <Link href="/courses">
              <Button variant="ghost" className="flex items-center space-x-2">
                <Video className="h-4 w-4" />
                <span className="hidden sm:inline">Courses</span>
              </Button>
            </Link>

            {user ? (
              <>
                {/* Shopping Cart */}
                <ShoppingCart />

                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span className="hidden sm:inline">{user.fullName || user.phone}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>My Account</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem asChild>
                      <Link href="/wallet" className="flex items-center">
                        <Wallet className="h-4 w-4 mr-2" />
                        Wallet
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem asChild>
                      <Link href="/orders" className="flex items-center">
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        My Orders
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link href="/reseller" className="flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        Reseller
                      </Link>
                    </DropdownMenuItem>

                    {user.role === "vendor" && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Vendor</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href="/vendor/dashboard" className="flex items-center">
                            <Store className="h-4 w-4 mr-2" />
                            Dashboard
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/vendor/products" className="flex items-center">
                            <Package className="h-4 w-4 mr-2" />
                            Products
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/vendor/orders" className="flex items-center">
                            <ShoppingBag className="h-4 w-4 mr-2" />
                            Orders
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/vendor/courses" className="flex items-center">
                            <Video className="h-4 w-4 mr-2" />
                            Courses
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}

                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="flex items-center">
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem onClick={logout} className="text-red-600">
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <Button variant="ghost" asChild>
                  <Link href="/auth/login">Sign In</Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/register">Get Started</Link>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
