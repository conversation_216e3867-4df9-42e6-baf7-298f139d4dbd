import { api } from "@/convex/_generated/api";
import { ConvexHttpClient } from "convex/browser";

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export interface ReferralData {
  userId: string;
  vendorId: string;
  productId?: string;
  commissionRate: number;
}

// Track referral click and store referral data in session/localStorage
export async function trackReferralClick(
  linkCode: string,
  ipAddress?: string,
  userAgent?: string
): Promise<ReferralData | null> {
  try {
    const result = await convex.mutation(api.resellers.trackReferralClick, {
      linkCode,
      ipAddress,
      userAgent,
    });

    if (result.success && result.referralLink) {
      // Store referral data in localStorage for later use during checkout
      const referralData: ReferralData = {
        userId: result.referralLink.userId,
        vendorId: result.referralLink.vendorId,
        productId: result.referralLink.productId,
        commissionRate: result.referralLink.commissionRate,
      };

      localStorage.setItem('referralData', JSON.stringify(referralData));
      localStorage.setItem('referralExpiry', (Date.now() + 24 * 60 * 60 * 1000).toString()); // 24 hours

      return referralData;
    }

    return null;
  } catch (error) {
    console.error('Failed to track referral click:', error);
    return null;
  }
}

// Get stored referral data
export function getReferralData(): ReferralData | null {
  try {
    const referralData = localStorage.getItem('referralData');
    const referralExpiry = localStorage.getItem('referralExpiry');

    if (!referralData || !referralExpiry) {
      return null;
    }

    // Check if referral has expired
    if (Date.now() > parseInt(referralExpiry)) {
      clearReferralData();
      return null;
    }

    return JSON.parse(referralData);
  } catch (error) {
    console.error('Failed to get referral data:', error);
    return null;
  }
}

// Clear referral data
export function clearReferralData(): void {
  localStorage.removeItem('referralData');
  localStorage.removeItem('referralExpiry');
}

// Check if current session has referral data
export function hasReferralData(): boolean {
  return getReferralData() !== null;
}

// Get referral commission rate
export function getReferralCommissionRate(): number {
  const referralData = getReferralData();
  return referralData?.commissionRate || 0;
}

// Get referrer user ID
export function getReferrerId(): string | null {
  const referralData = getReferralData();
  return referralData?.userId || null;
}
