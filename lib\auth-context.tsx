"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";

interface User {
  _id: Id<"users">;
  _creationTime: number;
  name: string;
  phone: string;
  role: "user" | "vendor" | "admin";
  address?: string;
  whatsappNumber?: string;
  profileImage?: string;
  isActive: boolean;
  isPro: boolean;
  isPhoneVerified: boolean;
  createdAt: number;
  updatedAt: number;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (phone: string) => Promise<{ success: boolean; user?: User; message: string }>;
  register: (data: {
    name: string;
    phone: string;
    role?: "user" | "vendor";
    address?: string;
    whatsappNumber?: string;
  }) => Promise<{ success: boolean; user?: User; message: string }>;
  logout: () => void;
  updateProfile: (data: {
    name?: string;
    address?: string;
    whatsappNumber?: string;
    profileImage?: string;
  }) => Promise<User | null>;
  refreshUser: () => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (role: string | string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [userId, setUserId] = useState<Id<"users"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get current user data
  const user = useQuery(api.auth.getCurrentUser, userId ? { userId } : { userId: undefined });
  
  // Mutations
  const loginMutation = useMutation(api.auth.loginUser);
  const registerMutation = useMutation(api.auth.registerUser);
  const updateProfileMutation = useMutation(api.auth.updateProfile);

  // Load user ID from localStorage on mount
  useEffect(() => {
    const storedUserId = localStorage.getItem("vendorshub_user_id");
    if (storedUserId) {
      setUserId(storedUserId as Id<"users">);
    }
    setIsLoading(false);
  }, []);

  // Update loading state based on user query
  useEffect(() => {
    if (userId && user !== undefined) {
      setIsLoading(false);
    }
  }, [userId, user]);

  const login = async (phone: string) => {
    try {
      setIsLoading(true);
      const result = await loginMutation({ phone });
      
      if (result.success && result.user) {
        setUserId(result.user._id);
        localStorage.setItem("vendorshub_user_id", result.user._id);
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Login failed"
      };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: {
    name: string;
    phone: string;
    role?: "user" | "vendor";
    address?: string;
    whatsappNumber?: string;
  }) => {
    try {
      setIsLoading(true);
      const result = await registerMutation(data);
      
      if (result.success && result.user) {
        setUserId(result.user._id);
        localStorage.setItem("vendorshub_user_id", result.user._id);
      }
      
      return result;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Registration failed"
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUserId(null);
    localStorage.removeItem("vendorshub_user_id");
  };

  const updateProfile = async (data: {
    name?: string;
    address?: string;
    whatsappNumber?: string;
    profileImage?: string;
  }) => {
    if (!userId) return null;
    
    try {
      const updatedUser = await updateProfileMutation({
        userId,
        ...data
      });
      return updatedUser;
    } catch (error) {
      console.error("Profile update failed:", error);
      return null;
    }
  };

  const isAuthenticated = !!user && user.isActive;

  const hasRole = (role: string | string[]) => {
    if (!user) return false;

    if (Array.isArray(role)) {
      return role.includes(user.role);
    }

    return user.role === role;
  };

  const refreshUser = async () => {
    // Force a re-fetch of user data by temporarily clearing and resetting userId
    if (userId) {
      const currentUserId = userId;
      setUserId(null);
      // Small delay to ensure the query is cleared
      setTimeout(() => {
        setUserId(currentUserId);
      }, 100);
    }
  };

  const contextValue: AuthContextType = {
    user: user || null,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    refreshUser,
    isAuthenticated,
    hasRole,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Custom hooks for role-based access
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login page
      window.location.href = "/auth/login";
    }
  }, [isAuthenticated, isLoading]);

  return { isAuthenticated, isLoading };
}

export function useRequireRole(requiredRole: string | string[]) {
  const { hasRole, isLoading, isAuthenticated } = useAuth();
  
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || !hasRole(requiredRole))) {
      // Redirect to unauthorized page or login
      window.location.href = "/unauthorized";
    }
  }, [hasRole, isLoading, isAuthenticated, requiredRole]);

  return { hasRole: hasRole(requiredRole), isLoading };
}
