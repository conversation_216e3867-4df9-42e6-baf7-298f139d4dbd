"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Crown, Check, X, Video, Star, Zap, Shield } from "lucide-react";
import { toast } from "sonner";

interface ProUpgradeProps {
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

export function ProUpgrade({ trigger, onSuccess }: ProUpgradeProps) {
  const { user, refreshUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);

  const upgradeUserToPro = useMutation(api.users.upgradeUserToPro);

  const handleUpgrade = async () => {
    if (!user) {
      toast.error("Please log in to upgrade");
      return;
    }

    if (user.isPro) {
      toast.info("You're already a Pro user!");
      return;
    }

    // Check wallet balance
    const walletBalance = user.walletBalance || 0;
    if (walletBalance < 5000) {
      toast.error("Insufficient wallet balance. Please fund your wallet first.");
      return;
    }

    setIsUpgrading(true);
    try {
      await upgradeUserToPro({ userId: user._id });
      await refreshUser();
      toast.success("Successfully upgraded to Pro! 🎉");
      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      toast.error("Failed to upgrade to Pro. Please try again.");
    } finally {
      setIsUpgrading(false);
    }
  };

  const proFeatures = [
    {
      icon: Video,
      title: "Unlimited Course Access",
      description: "Access all premium courses and educational content"
    },
    {
      icon: Star,
      title: "Priority Support",
      description: "Get priority customer support and assistance"
    },
    {
      icon: Zap,
      title: "Advanced Features",
      description: "Access to beta features and advanced tools"
    },
    {
      icon: Shield,
      title: "Enhanced Security",
      description: "Additional security features and account protection"
    }
  ];

  const defaultTrigger = (
    <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
      <Crown className="h-4 w-4 mr-2" />
      Upgrade to Pro
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center text-2xl">
            <Crown className="h-6 w-6 mr-2 text-purple-600" />
            Upgrade to Pro
          </DialogTitle>
          <DialogDescription>
            Unlock premium features and get unlimited access to all courses
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Pricing Card */}
          <Card className="border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
              <CardTitle className="text-3xl font-bold text-purple-900">
                ₦5,000
                <span className="text-lg font-normal text-gray-600">/lifetime</span>
              </CardTitle>
              <CardDescription className="text-purple-700">
                One-time payment for lifetime Pro access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {proFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                      <feature.icon className="h-3 w-3 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{feature.title}</p>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Comparison Table */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="text-center pb-3">
                <CardTitle className="text-lg">Free User</CardTitle>
                <Badge variant="outline">Current Plan</Badge>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Basic course access</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Product reselling</span>
                </div>
                <div className="flex items-center space-x-2">
                  <X className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-gray-500">Premium courses</span>
                </div>
                <div className="flex items-center space-x-2">
                  <X className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-gray-500">Priority support</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-purple-50">
              <CardHeader className="text-center pb-3">
                <CardTitle className="text-lg text-purple-900">Pro User</CardTitle>
                <Badge className="bg-purple-600">Upgrade</Badge>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Everything in Free</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">All premium courses</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Priority support</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Advanced features</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Wallet Balance Check */}
          {user && (
            <Card className={`${(user.walletBalance || 0) < 5000 ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Your Wallet Balance</p>
                    <p className="text-2xl font-bold">₦{(user.walletBalance || 0).toLocaleString()}</p>
                  </div>
                  {(user.walletBalance || 0) < 5000 && (
                    <div className="text-right">
                      <p className="text-sm text-red-600 font-medium">Insufficient Balance</p>
                      <p className="text-xs text-red-500">
                        Need ₦{(5000 - (user.walletBalance || 0)).toLocaleString()} more
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Maybe Later
            </Button>
            <Button
              onClick={handleUpgrade}
              disabled={isUpgrading || !user || user.isPro || (user.walletBalance || 0) < 5000}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {isUpgrading ? (
                "Upgrading..."
              ) : user?.isPro ? (
                "Already Pro"
              ) : (user?.walletBalance || 0) < 5000 ? (
                "Insufficient Balance"
              ) : (
                <>
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade Now
                </>
              )}
            </Button>
          </div>

          {(user?.walletBalance || 0) < 5000 && (
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">
                Need to fund your wallet first?
              </p>
              <Button variant="outline" size="sm" asChild>
                <a href="/wallet">Fund Wallet</a>
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
