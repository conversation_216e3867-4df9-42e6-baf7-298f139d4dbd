"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Store, Globe, Smartphone } from "lucide-react";

export default function VendorSetupPage() {
  const { user } = useAuth();
  const router = useRouter();
  const createVendor = useMutation(api.vendors.createVendor);
  
  const [formData, setFormData] = useState({
    subdomain: "",
    shopName: "",
    shopDescription: "",
    emoji: "🛍️",
    defaultWhatsappNumber: "",
    businessAddress: "",
    businessPhone: "",
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Redirect if not a vendor
  if (user && user.role !== "vendor") {
    router.push("/dashboard");
    return null;
  }

  // Redirect if not authenticated
  if (!user) {
    router.push("/auth/login");
    return null;
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError("");
  };

  const validateSubdomain = (subdomain: string) => {
    // Check if subdomain is valid (alphanumeric, hyphens, 3-30 characters)
    const subdomainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,28}[a-zA-Z0-9]$/;
    return subdomainRegex.test(subdomain);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      // Validate required fields
      if (!formData.subdomain || !formData.shopName) {
        throw new Error("Subdomain and shop name are required");
      }

      // Validate subdomain format
      if (!validateSubdomain(formData.subdomain)) {
        throw new Error("Subdomain must be 3-30 characters, alphanumeric with hyphens allowed (not at start/end)");
      }

      // Create vendor shop
      const vendor = await createVendor({
        userId: user._id,
        subdomain: formData.subdomain.toLowerCase(),
        shopName: formData.shopName,
        shopDescription: formData.shopDescription || undefined,
        emoji: formData.emoji,
        defaultWhatsappNumber: formData.defaultWhatsappNumber || undefined,
        businessAddress: formData.businessAddress || undefined,
        businessPhone: formData.businessPhone || undefined,
      });

      setSuccess("Your vendor shop has been created successfully!");
      
      // Redirect to vendor dashboard after a short delay
      setTimeout(() => {
        router.push("/vendor/dashboard");
      }, 2000);

    } catch (error: any) {
      console.error("Vendor setup failed:", error);
      setError(error.message || "Failed to create vendor shop. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Store className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Setup Your Vendor Shop
          </h1>
          <p className="text-lg text-gray-600">
            Create your custom subdomain and start selling your products
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Shop Information</CardTitle>
            <CardDescription>
              Fill in the details below to create your vendor shop with a custom subdomain
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert className="mb-6 border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-6 border-green-200 bg-green-50">
                <AlertDescription className="text-green-800">
                  {success}
                </AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="subdomain">
                  Subdomain <span className="text-red-500">*</span>
                </Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="subdomain"
                    type="text"
                    placeholder="yourshop"
                    value={formData.subdomain}
                    onChange={(e) => handleInputChange("subdomain", e.target.value)}
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <span className="text-gray-500 text-sm">.vendorshub.com</span>
                </div>
                <p className="text-sm text-gray-500">
                  Choose a unique subdomain for your shop (3-30 characters, letters, numbers, and hyphens)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shopName">
                  Shop Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="shopName"
                  type="text"
                  placeholder="My Awesome Shop"
                  value={formData.shopName}
                  onChange={(e) => handleInputChange("shopName", e.target.value)}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="emoji">Shop Emoji</Label>
                <Input
                  id="emoji"
                  type="text"
                  placeholder="🛍️"
                  value={formData.emoji}
                  onChange={(e) => handleInputChange("emoji", e.target.value)}
                  disabled={isLoading}
                  maxLength={2}
                />
                <p className="text-sm text-gray-500">
                  Choose an emoji to represent your shop
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shopDescription">Shop Description</Label>
                <Textarea
                  id="shopDescription"
                  placeholder="Tell customers about your shop and what you sell..."
                  value={formData.shopDescription}
                  onChange={(e) => handleInputChange("shopDescription", e.target.value)}
                  disabled={isLoading}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultWhatsappNumber">
                  <Smartphone className="inline h-4 w-4 mr-1" />
                  Default WhatsApp Number
                </Label>
                <Input
                  id="defaultWhatsappNumber"
                  type="tel"
                  placeholder="+234XXXXXXXXXX"
                  value={formData.defaultWhatsappNumber}
                  onChange={(e) => handleInputChange("defaultWhatsappNumber", e.target.value)}
                  disabled={isLoading}
                />
                <p className="text-sm text-gray-500">
                  This will be used for customer inquiries and orders
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessAddress">Business Address</Label>
                <Textarea
                  id="businessAddress"
                  placeholder="Your business address..."
                  value={formData.businessAddress}
                  onChange={(e) => handleInputChange("businessAddress", e.target.value)}
                  disabled={isLoading}
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessPhone">Business Phone</Label>
                <Input
                  id="businessPhone"
                  type="tel"
                  placeholder="+234XXXXXXXXXX"
                  value={formData.businessPhone}
                  onChange={(e) => handleInputChange("businessPhone", e.target.value)}
                  disabled={isLoading}
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Your Shop...
                  </>
                ) : (
                  <>
                    <Globe className="mr-2 h-4 w-4" />
                    Create My Shop
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
