"use client";

import Link from "next/link";
import { useAuth } from "@/lib/auth-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Home, LogIn } from "lucide-react";

export default function UnauthorizedPage() {
  const { isAuthenticated, user } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            You don't have permission to access this page
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Unauthorized Access</CardTitle>
            <CardDescription>
              {isAuthenticated 
                ? `Your current role (${user?.role}) doesn't have access to this resource.`
                : "You need to sign in to access this page."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {isAuthenticated ? (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  If you believe this is an error, please contact support or check if you need to upgrade your account.
                </p>
                <div className="flex flex-col space-y-2">
                  <Button asChild>
                    <Link href="/">
                      <Home className="mr-2 h-4 w-4" />
                      Go to Homepage
                    </Link>
                  </Button>
                  {user?.role === "user" && (
                    <Button variant="outline" asChild>
                      <Link href="/dashboard">
                        Go to Dashboard
                      </Link>
                    </Button>
                  )}
                  {user?.role === "vendor" && (
                    <Button variant="outline" asChild>
                      <Link href="/vendor/dashboard">
                        Go to Vendor Dashboard
                      </Link>
                    </Button>
                  )}
                  {user?.role === "reseller" && (
                    <Button variant="outline" asChild>
                      <Link href="/reseller/dashboard">
                        Go to Reseller Dashboard
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Please sign in to your account to continue.
                </p>
                <div className="flex flex-col space-y-2">
                  <Button asChild>
                    <Link href="/auth/login">
                      <LogIn className="mr-2 h-4 w-4" />
                      Sign In
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/auth/register">
                      Create Account
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Need help?{" "}
            <Link href="/support" className="text-blue-600 hover:text-blue-500">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
