import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new product
export const createProduct = mutation({
  args: {
    vendorId: v.id("vendors"),
    name: v.string(),
    description: v.string(),
    price: v.number(),
    compareAtPrice: v.optional(v.number()),
    images: v.array(v.string()),
    videos: v.optional(v.array(v.string())),
    category: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    stockQuantity: v.optional(v.number()),
    isUnlimitedStock: v.boolean(),
    isDigital: v.boolean(),
    whatsappNumber: v.optional(v.string()),
    whatsappMessage: v.optional(v.string()),
    slug: v.optional(v.string()),
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Verify vendor exists
    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) {
      throw new Error("Vendor not found");
    }

    // Generate slug if not provided
    let slug = args.slug;
    if (!slug) {
      slug = args.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Check if slug is unique for this vendor
    const existingProduct = await ctx.db
      .query("products")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();

    if (existingProduct && existingProduct.vendorId === args.vendorId) {
      slug = `${slug}-${Date.now()}`;
    }

    const now = Date.now();
    
    const productId = await ctx.db.insert("products", {
      vendorId: args.vendorId,
      name: args.name,
      description: args.description,
      price: args.price,
      compareAtPrice: args.compareAtPrice,
      images: args.images,
      videos: args.videos,
      category: args.category,
      tags: args.tags,
      stockQuantity: args.stockQuantity,
      isUnlimitedStock: args.isUnlimitedStock,
      isActive: true,
      isDigital: args.isDigital,
      whatsappNumber: args.whatsappNumber,
      whatsappMessage: args.whatsappMessage,
      slug,
      metaTitle: args.metaTitle,
      metaDescription: args.metaDescription,
      createdAt: now,
      updatedAt: now,
    });

    return productId;
  },
});

// Get products by vendor
export const getProductsByVendor = query({
  args: {
    vendorId: v.id("vendors"),
    activeOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("products")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId));

    const products = args.limit
      ? await query.take(args.limit)
      : await query.collect();

    if (args.activeOnly) {
      return products.filter(p => p.isActive);
    }

    return products;
  },
});

// Get product by ID
export const getProductById = query({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    
    if (!product) {
      return null;
    }

    // Get vendor information
    const vendor = await ctx.db.get(product.vendorId);
    
    return {
      ...product,
      vendor,
    };
  },
});

// Get product by slug and vendor
export const getProductBySlug = query({
  args: { 
    slug: v.string(),
    vendorId: v.id("vendors"),
  },
  handler: async (ctx, args) => {
    const product = await ctx.db
      .query("products")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .filter((q) => q.eq(q.field("vendorId"), args.vendorId))
      .first();

    if (!product) {
      return null;
    }

    // Get vendor information
    const vendor = await ctx.db.get(product.vendorId);
    
    return {
      ...product,
      vendor,
    };
  },
});

// Update product
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    compareAtPrice: v.optional(v.number()),
    images: v.optional(v.array(v.string())),
    videos: v.optional(v.array(v.string())),
    category: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    stockQuantity: v.optional(v.number()),
    isUnlimitedStock: v.optional(v.boolean()),
    isDigital: v.optional(v.boolean()),
    whatsappNumber: v.optional(v.string()),
    whatsappMessage: v.optional(v.string()),
    slug: v.optional(v.string()),
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { productId, ...updates } = args;
    
    await ctx.db.patch(productId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(productId);
  },
});

// Toggle product active status
export const toggleProductStatus = mutation({
  args: { 
    productId: v.id("products"),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.productId, {
      isActive: args.isActive,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.productId);
  },
});

// Delete product
export const deleteProduct = mutation({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.productId);
    return { success: true };
  },
});

// Get products by category
export const getProductsByCategory = query({
  args: {
    category: v.string(),
    limit: v.optional(v.number()),
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("category", args.category));

    const products = args.limit
      ? await query.take(args.limit)
      : await query.collect();

    if (args.activeOnly) {
      return products.filter(p => p.isActive);
    }

    return products;
  },
});

// Search products
export const searchProducts = query({
  args: {
    searchTerm: v.string(),
    vendorId: v.optional(v.id("vendors")),
    category: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let products;

    if (args.vendorId) {
      const query = ctx.db
        .query("products")
        .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId!));
      products = args.limit ? await query.take(args.limit) : await query.collect();
    } else {
      const query = ctx.db
        .query("products")
        .withIndex("by_active", (q) => q.eq("isActive", true));
      products = args.limit ? await query.take(args.limit) : await query.collect();
    }

    // Filter by search term (simple text search)
    const searchTerm = args.searchTerm.toLowerCase();
    const filteredProducts = products.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );

    // Filter by category if provided
    if (args.category) {
      return filteredProducts.filter(product => product.category === args.category);
    }

    return filteredProducts;
  },
});

// Get featured products (most recent active products)
export const getFeaturedProducts = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("products")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("desc");

    return args.limit
      ? await query.take(args.limit)
      : await query.collect();
  },
});
