import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Create a new vendor shop
export const createVendor = mutation({
  args: {
    userId: v.id("users"),
    subdomain: v.string(),
    shopName: v.string(),
    shopDescription: v.optional(v.string()),
    emoji: v.string(),
    defaultWhatsappNumber: v.optional(v.string()),
    businessAddress: v.optional(v.string()),
    businessPhone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if subdomain is already taken
    const existingVendor = await ctx.db
      .query("vendors")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain))
      .first();

    if (existingVendor) {
      throw new Error("Subdomain is already taken");
    }

    // Check if user already has a vendor shop
    const existingUserVendor = await ctx.db
      .query("vendors")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (existingUserVendor) {
      throw new Error("User already has a vendor shop");
    }

    // Verify user exists and has vendor role
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    if (user.role !== "vendor") {
      throw new Error("User must have vendor role to create a shop");
    }

    const now = Date.now();
    
    const vendorId = await ctx.db.insert("vendors", {
      userId: args.userId,
      subdomain: args.subdomain.toLowerCase(),
      shopName: args.shopName,
      shopDescription: args.shopDescription,
      emoji: args.emoji,
      defaultWhatsappNumber: args.defaultWhatsappNumber,
      businessAddress: args.businessAddress,
      businessPhone: args.businessPhone,
      isShopActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return vendorId;
  },
});

// Get vendor by subdomain
export const getVendorBySubdomain = query({
  args: { subdomain: v.string() },
  handler: async (ctx, args) => {
    const vendor = await ctx.db
      .query("vendors")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain.toLowerCase()))
      .first();

    if (!vendor) {
      return null;
    }

    // Get the associated user data
    const user = await ctx.db.get(vendor.userId);
    
    return {
      ...vendor,
      user,
    };
  },
});

// Get vendor by user ID
export const getVendorByUserId = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const vendor = await ctx.db
      .query("vendors")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!vendor) {
      return null;
    }

    // Get the associated user data
    const user = await ctx.db.get(vendor.userId);
    
    return {
      ...vendor,
      user,
    };
  },
});

// Get vendor by ID
export const getVendorById = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    const vendor = await ctx.db.get(args.vendorId);
    
    if (!vendor) {
      return null;
    }

    // Get the associated user data
    const user = await ctx.db.get(vendor.userId);
    
    return {
      ...vendor,
      user,
    };
  },
});

// Update vendor shop
export const updateVendor = mutation({
  args: {
    vendorId: v.id("vendors"),
    shopName: v.optional(v.string()),
    shopDescription: v.optional(v.string()),
    shopLogo: v.optional(v.string()),
    shopBanner: v.optional(v.string()),
    emoji: v.optional(v.string()),
    defaultWhatsappNumber: v.optional(v.string()),
    businessAddress: v.optional(v.string()),
    businessPhone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { vendorId, ...updates } = args;
    
    await ctx.db.patch(vendorId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(vendorId);
  },
});

// Check if subdomain is available
export const isSubdomainAvailable = query({
  args: { subdomain: v.string() },
  handler: async (ctx, args) => {
    const existingVendor = await ctx.db
      .query("vendors")
      .withIndex("by_subdomain", (q) => q.eq("subdomain", args.subdomain.toLowerCase()))
      .first();

    return !existingVendor;
  },
});

// Get all active vendors
export const getActiveVendors = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const query = ctx.db
      .query("vendors")
      .withIndex("by_active", (q) => q.eq("isShopActive", true));

    const vendors = args.limit
      ? await query.take(args.limit)
      : await query.collect();

    // Get user data for each vendor
    const vendorsWithUsers = await Promise.all(
      vendors.map(async (vendor) => {
        const user = await ctx.db.get(vendor.userId);
        return {
          ...vendor,
          user,
        };
      })
    );

    return vendorsWithUsers;
  },
});

// Activate vendor shop
export const activateVendorShop = mutation({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.vendorId, {
      isShopActive: true,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.vendorId);
  },
});

// Deactivate vendor shop
export const deactivateVendorShop = mutation({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.vendorId, {
      isShopActive: false,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(args.vendorId);
  },
});

// Get vendor statistics
export const getVendorStats = query({
  args: { vendorId: v.id("vendors") },
  handler: async (ctx, args) => {
    // Get product count
    const products = await ctx.db
      .query("products")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId))
      .collect();

    const activeProducts = products.filter(p => p.isActive);

    // Get order count
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_vendor", (q) => q.eq("vendorId", args.vendorId))
      .collect();

    const completedOrders = orders.filter(o => o.status === "delivered");
    const totalRevenue = completedOrders.reduce((sum, order) => sum + order.total, 0);

    return {
      totalProducts: products.length,
      activeProducts: activeProducts.length,
      totalOrders: orders.length,
      completedOrders: completedOrders.length,
      totalRevenue,
    };
  },
});
